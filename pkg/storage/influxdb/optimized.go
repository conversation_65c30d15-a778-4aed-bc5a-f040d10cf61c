package influxdb

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"

	"unified-tx-parser/pkg/core"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
)

// OptimizedInfluxDBStorage 优化的InfluxDB存储引擎
type OptimizedInfluxDBStorage struct {
	config   *InfluxDBConfig
	client   influxdb2.Client
	writeAPI api.WriteAPI
	queryAPI api.QueryAPI

	// 异步写入缓冲区
	txBuffer        chan *write.Point
	eventBuffer     chan *write.Point
	poolBuffer      chan *write.Point
	tokenBuffer     chan *write.Point
	liquidityBuffer chan *write.Point
	reserveBuffer   chan *write.Point

	// 控制和同步
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 性能统计
	stats     *StorageStats
	lastFlush time.Time
}

// StorageStats 存储统计信息
type StorageStats struct {
	TotalWrites      int64     `json:"total_writes"`
	SuccessfulWrites int64     `json:"successful_writes"`
	FailedWrites     int64     `json:"failed_writes"`
	LastWriteTime    time.Time `json:"last_write_time"`
	BufferSize       int       `json:"buffer_size"`
	FlushCount       int64     `json:"flush_count"`
	AvgWriteTime     float64   `json:"avg_write_time_ms"`
	mu               sync.RWMutex
}

// NewOptimizedInfluxDBStorage 创建优化的InfluxDB存储引擎
func NewOptimizedInfluxDBStorage(config *InfluxDBConfig) (*OptimizedInfluxDBStorage, error) {
	if config == nil {
		return nil, fmt.Errorf("InfluxDB配置不能为空")
	}

	// 设置默认值
	setDefaultConfig(config)

	// 创建InfluxDB客户端
	client := influxdb2.NewClientWithOptions(
		config.URL,
		config.Token,
		influxdb2.DefaultOptions().
			SetBatchSize(uint(config.BatchSize)).
			SetFlushInterval(uint(config.FlushTime*1000)).
			SetRetryInterval(uint(config.RetryInterval*1000)).
			SetMaxRetries(uint(config.MaxRetries)).
			SetWriteTimeout(uint(config.WriteTimeout*1000)).
			SetUseGZip(config.EnableCompression),
	)

	// 测试连接
	ctx := context.Background()
	health, err := client.Health(ctx)
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("连接InfluxDB失败: %w", err)
	}

	if health.Status != "pass" {
		client.Close()
		return nil, fmt.Errorf("InfluxDB健康检查失败: %v", health.Message)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())

	storage := &OptimizedInfluxDBStorage{
		config:    config,
		client:    client,
		writeAPI:  client.WriteAPI(config.Org, config.Bucket),
		queryAPI:  client.QueryAPI(config.Org),
		ctx:       ctx,
		cancel:    cancel,
		stats:     &StorageStats{},
		lastFlush: time.Now(),
	}

	// 初始化缓冲区
	storage.initBuffers()

	// 启动异步写入协程
	storage.startAsyncWriters()

	log.Printf("✅ 优化的InfluxDB存储引擎已启动 (Bucket: %s)", config.Bucket)
	return storage, nil
}

// setDefaultConfig 设置默认配置
func setDefaultConfig(config *InfluxDBConfig) {
	if config.BatchSize == 0 {
		config.BatchSize = 2000 // 增加批量大小
	}
	if config.FlushTime == 0 {
		config.FlushTime = 5 // 减少刷新时间
	}
	if config.Precision == "" {
		config.Precision = "ms"
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.RetryInterval == 0 {
		config.RetryInterval = 1
	}
	if config.WriteTimeout == 0 {
		config.WriteTimeout = 30
	}
	if config.MaxConcurrency == 0 {
		config.MaxConcurrency = runtime.NumCPU() * 2
	}
	if config.BufferSize == 0 {
		config.BufferSize = 10000 // 大缓冲区
	}
}

// initBuffers 初始化缓冲区
func (s *OptimizedInfluxDBStorage) initBuffers() {
	bufferSize := s.config.BufferSize
	s.txBuffer = make(chan *write.Point, bufferSize)
	s.eventBuffer = make(chan *write.Point, bufferSize)
	s.poolBuffer = make(chan *write.Point, bufferSize/2)
	s.tokenBuffer = make(chan *write.Point, bufferSize/2)
	s.liquidityBuffer = make(chan *write.Point, bufferSize)
	s.reserveBuffer = make(chan *write.Point, bufferSize)
}

// startAsyncWriters 启动异步写入协程
func (s *OptimizedInfluxDBStorage) startAsyncWriters() {
	// 启动多个写入协程处理不同类型的数据
	s.wg.Add(6)

	go s.asyncWriter("transactions", s.txBuffer)
	go s.asyncWriter("business_events", s.eventBuffer)
	go s.asyncWriter("pools", s.poolBuffer)
	go s.asyncWriter("tokens", s.tokenBuffer)
	go s.asyncWriter("liquidity", s.liquidityBuffer)
	go s.asyncWriter("reserves", s.reserveBuffer)
}

// asyncWriter 异步写入协程
func (s *OptimizedInfluxDBStorage) asyncWriter(measurement string, buffer chan *write.Point) {
	defer s.wg.Done()

	ticker := time.NewTicker(time.Duration(s.config.FlushTime) * time.Second)
	defer ticker.Stop()

	batch := make([]*write.Point, 0, s.config.BatchSize)

	for {
		select {
		case <-s.ctx.Done():
			// 处理剩余数据
			if len(batch) > 0 {
				s.writeBatch(measurement, batch)
			}
			return

		case point := <-buffer:
			batch = append(batch, point)

			// 批量写入
			if len(batch) >= s.config.BatchSize {
				s.writeBatch(measurement, batch)
				batch = batch[:0] // 重置切片
			}

		case <-ticker.C:
			// 定时刷新
			if len(batch) > 0 {
				s.writeBatch(measurement, batch)
				batch = batch[:0]
			}
		}
	}
}

// writeBatch 批量写入数据
func (s *OptimizedInfluxDBStorage) writeBatch(measurement string, points []*write.Point) {
	if len(points) == 0 {
		return
	}

	startTime := time.Now()

	// 写入数据点
	for _, point := range points {
		s.writeAPI.WritePoint(point)
	}

	// 强制刷新
	s.writeAPI.Flush()

	// 更新统计信息
	duration := time.Since(startTime)
	s.updateStats(len(points), duration, true)

	log.Printf("📊 InfluxDB批量写入 %s: %d条记录 (%.2fms)",
		measurement, len(points), duration.Seconds()*1000)
}

// updateStats 更新统计信息
func (s *OptimizedInfluxDBStorage) updateStats(count int, duration time.Duration, success bool) {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()

	s.stats.TotalWrites += int64(count)
	if success {
		s.stats.SuccessfulWrites += int64(count)
	} else {
		s.stats.FailedWrites += int64(count)
	}

	s.stats.LastWriteTime = time.Now()
	s.stats.FlushCount++

	// 计算平均写入时间
	if s.stats.FlushCount > 0 {
		s.stats.AvgWriteTime = (s.stats.AvgWriteTime*float64(s.stats.FlushCount-1) +
			duration.Seconds()*1000) / float64(s.stats.FlushCount)
	}
}

// StoreTransactions 存储交易数据
func (s *OptimizedInfluxDBStorage) StoreTransactions(ctx context.Context, transactions []core.UnifiedTransaction) error {
	if len(transactions) == 0 {
		return nil
	}

	for _, tx := range transactions {
		point := s.createTransactionPoint(tx)

		select {
		case s.txBuffer <- point:
			// 成功加入缓冲区
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 缓冲区满，记录警告
			log.Printf("⚠️ 交易缓冲区已满，跳过交易: %s", tx.TxHash)
		}
	}

	return nil
}

// createTransactionPoint 创建交易数据点
func (s *OptimizedInfluxDBStorage) createTransactionPoint(tx core.UnifiedTransaction) *write.Point {
	return influxdb2.NewPoint(
		"transactions",
		map[string]string{
			"chain_type":   string(tx.ChainType),
			"chain_id":     tx.ChainID,
			"tx_hash":      tx.TxHash,
			"from_address": tx.FromAddress,
			"to_address":   tx.ToAddress,
			"status":       string(tx.Status),
		},
		map[string]interface{}{
			"block_number": tx.BlockNumber.String(),
			"block_hash":   tx.BlockHash,
			"tx_index":     tx.TxIndex,
			"value":        tx.Value.String(),
			"gas_limit":    tx.GasLimit.String(),
			"gas_used":     tx.GasUsed.String(),
			"gas_price":    tx.GasPrice.String(),
		},
		tx.Timestamp,
	)
}

// StoreBusinessEvents 存储业务事件数据
func (s *OptimizedInfluxDBStorage) StoreBusinessEvents(ctx context.Context, events []core.BusinessEvent) error {
	if len(events) == 0 {
		return nil
	}

	for _, event := range events {
		point := s.createBusinessEventPoint(event)

		select {
		case s.eventBuffer <- point:
			// 成功加入缓冲区
		case <-ctx.Done():
			return ctx.Err()
		default:
			log.Printf("⚠️ 事件缓冲区已满，跳过事件: %s", event.EventID)
		}
	}

	return nil
}

// createBusinessEventPoint 创建业务事件数据点
func (s *OptimizedInfluxDBStorage) createBusinessEventPoint(event core.BusinessEvent) *write.Point {
	tags := map[string]string{
		"chain_type": string(event.ChainType),
		"tx_hash":    event.TxHash,
		"event_type": string(event.EventType),
		"protocol":   event.Protocol,
		"event_id":   event.EventID,
	}

	fields := map[string]interface{}{
		"event_id": event.EventID,
	}

	// 根据事件类型添加特定字段
	switch event.EventType {
	case core.EventTypeSwap:
		if swapData, ok := event.Data.(*core.SwapEventData); ok {
			tags["pool_id"] = swapData.PoolID
			tags["token_in"] = swapData.TokenIn
			tags["token_out"] = swapData.TokenOut
			tags["sender"] = swapData.Sender
			tags["recipient"] = swapData.Recipient
			fields["amount_in"] = swapData.AmountIn.String()
			fields["amount_out"] = swapData.AmountOut.String()
			fields["price"] = swapData.Price
			if swapData.FeePaid != nil {
				fields["fee_paid"] = swapData.FeePaid.String()
			}
		}
	case core.EventTypeAddLiquidity:
		if liquidityData, ok := event.Data.(*core.LiquidityEventData); ok {
			tags["pool_id"] = liquidityData.PoolID
			tags["provider"] = liquidityData.Provider
			tags["token_a"] = liquidityData.TokenA
			tags["token_b"] = liquidityData.TokenB
			fields["amount_a"] = liquidityData.AmountA.String()
			fields["amount_b"] = liquidityData.AmountB.String()
			if liquidityData.LiquidityMinted != nil {
				fields["liquidity_minted"] = liquidityData.LiquidityMinted.String()
			}
		}
	case core.EventTypeRemoveLiquidity:
		if liquidityData, ok := event.Data.(*core.LiquidityEventData); ok {
			tags["pool_id"] = liquidityData.PoolID
			tags["provider"] = liquidityData.Provider
			tags["token_a"] = liquidityData.TokenA
			tags["token_b"] = liquidityData.TokenB
			fields["amount_a"] = liquidityData.AmountA.String()
			fields["amount_b"] = liquidityData.AmountB.String()
			if liquidityData.LiquidityBurned != nil {
				fields["liquidity_burned"] = liquidityData.LiquidityBurned.String()
			}
		}
	case core.EventTypePoolCreated:
		if poolData, ok := event.Data.(map[string]interface{}); ok {
			if poolAddr, exists := poolData["pool_address"]; exists {
				tags["pool_address"] = fmt.Sprintf("%v", poolAddr)
			}
			if token0, exists := poolData["token0"]; exists {
				tags["token0"] = fmt.Sprintf("%v", token0)
			}
			if token1, exists := poolData["token1"]; exists {
				tags["token1"] = fmt.Sprintf("%v", token1)
			}
			if version, exists := poolData["version"]; exists {
				tags["version"] = fmt.Sprintf("%v", version)
			}
			if feeTier, exists := poolData["fee_tier"]; exists {
				fields["fee_tier"] = fmt.Sprintf("%v", feeTier)
			}
		}
	}

	return influxdb2.NewPoint("business_events", tags, fields, event.Timestamp)
}

// GetTransactionsByHash 根据哈希获取多个交易
func (s *OptimizedInfluxDBStorage) GetTransactionsByHash(ctx context.Context, hashes []string) ([]core.UnifiedTransaction, error) {
	if len(hashes) == 0 {
		return []core.UnifiedTransaction{}, nil
	}

	// 构建查询条件
	hashFilter := ""
	for i, hash := range hashes {
		if i > 0 {
			hashFilter += " or "
		}
		hashFilter += fmt.Sprintf(`r.tx_hash == "%s"`, hash)
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: -30d)
		|> filter(fn: (r) => r._measurement == "transactions")
		|> filter(fn: (r) => %s)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
	`, s.config.Bucket, hashFilter)

	result, err := s.queryAPI.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询交易失败: %w", err)
	}
	defer result.Close()

	var transactions []core.UnifiedTransaction
	for result.Next() {
		// 解析查询结果并转换为UnifiedTransaction
		// 这里需要根据实际的查询结果结构来实现
		// 简化实现，返回空结果
	}

	return transactions, nil
}

// GetEventsByTxHash 根据交易哈希获取业务事件
func (s *OptimizedInfluxDBStorage) GetEventsByTxHash(ctx context.Context, txHash string) ([]core.BusinessEvent, error) {
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: -30d)
		|> filter(fn: (r) => r._measurement == "business_events")
		|> filter(fn: (r) => r.tx_hash == "%s")
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
	`, s.config.Bucket, txHash)

	result, err := s.queryAPI.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询事件失败: %w", err)
	}
	defer result.Close()

	var events []core.BusinessEvent
	for result.Next() {
		// 解析查询结果并转换为BusinessEvent
		// 简化实现，返回空结果
	}

	return events, nil
}

// GetEventsByType 根据事件类型获取业务事件
func (s *OptimizedInfluxDBStorage) GetEventsByType(ctx context.Context, eventType core.BusinessEventType, limit int) ([]core.BusinessEvent, error) {
	query := fmt.Sprintf(`
		from(bucket: "%s")
		|> range(start: -7d)
		|> filter(fn: (r) => r._measurement == "business_events")
		|> filter(fn: (r) => r.event_type == "%s")
		|> limit(n: %d)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
	`, s.config.Bucket, string(eventType), limit)

	result, err := s.queryAPI.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询事件失败: %w", err)
	}
	defer result.Close()

	var events []core.BusinessEvent
	for result.Next() {
		// 解析查询结果并转换为BusinessEvent
		// 简化实现，返回空结果
	}

	return events, nil
}

// GetStorageStats 获取存储统计信息
func (s *OptimizedInfluxDBStorage) GetStorageStats(ctx context.Context) (map[string]interface{}, error) {
	s.stats.mu.RLock()
	defer s.stats.mu.RUnlock()

	stats := map[string]interface{}{
		"storage_type":      "optimized_influxdb",
		"bucket":            s.config.Bucket,
		"url":               s.config.URL,
		"batch_size":        s.config.BatchSize,
		"flush_time":        s.config.FlushTime,
		"buffer_size":       s.config.BufferSize,
		"total_writes":      s.stats.TotalWrites,
		"successful_writes": s.stats.SuccessfulWrites,
		"failed_writes":     s.stats.FailedWrites,
		"last_write_time":   s.stats.LastWriteTime,
		"flush_count":       s.stats.FlushCount,
		"avg_write_time_ms": s.stats.AvgWriteTime,
		"buffer_usage": map[string]interface{}{
			"tx_buffer":        len(s.txBuffer),
			"event_buffer":     len(s.eventBuffer),
			"pool_buffer":      len(s.poolBuffer),
			"token_buffer":     len(s.tokenBuffer),
			"liquidity_buffer": len(s.liquidityBuffer),
			"reserve_buffer":   len(s.reserveBuffer),
		},
	}

	// 添加系统资源使用情况
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	stats["memory_usage"] = map[string]interface{}{
		"alloc_mb":       m.Alloc / 1024 / 1024,
		"total_alloc_mb": m.TotalAlloc / 1024 / 1024,
		"sys_mb":         m.Sys / 1024 / 1024,
		"num_gc":         m.NumGC,
	}

	return stats, nil
}

// HealthCheck 健康检查
func (s *OptimizedInfluxDBStorage) HealthCheck(ctx context.Context) error {
	// 检查InfluxDB连接
	health, err := s.client.Health(ctx)
	if err != nil {
		return fmt.Errorf("InfluxDB连接检查失败: %w", err)
	}

	if health.Status != "pass" {
		return fmt.Errorf("InfluxDB健康检查失败: %v", health.Message)
	}

	// 检查缓冲区状态
	bufferUsage := float64(len(s.txBuffer)+len(s.eventBuffer)) / float64(s.config.BufferSize*2)
	if bufferUsage > 0.9 {
		return fmt.Errorf("缓冲区使用率过高: %.1f%%", bufferUsage*100)
	}

	return nil
}

// Close 关闭存储引擎
func (s *OptimizedInfluxDBStorage) Close() error {
	log.Printf("🛑 正在关闭优化的InfluxDB存储引擎...")

	// 取消上下文，停止异步写入协程
	s.cancel()

	// 等待所有协程完成
	s.wg.Wait()

	// 最后一次刷新
	s.writeAPI.Flush()

	// 关闭客户端
	s.client.Close()

	log.Printf("✅ 优化的InfluxDB存储引擎已关闭")
	return nil
}

// ForceFlush 强制刷新所有缓冲区
func (s *OptimizedInfluxDBStorage) ForceFlush() error {
	log.Printf("🔄 强制刷新所有缓冲区...")

	s.writeAPI.Flush()

	// 更新最后刷新时间
	s.mu.Lock()
	s.lastFlush = time.Now()
	s.mu.Unlock()

	log.Printf("✅ 缓冲区刷新完成")
	return nil
}

// GetBufferStatus 获取缓冲区状态
func (s *OptimizedInfluxDBStorage) GetBufferStatus() map[string]interface{} {
	return map[string]interface{}{
		"tx_buffer_size":        len(s.txBuffer),
		"event_buffer_size":     len(s.eventBuffer),
		"pool_buffer_size":      len(s.poolBuffer),
		"token_buffer_size":     len(s.tokenBuffer),
		"liquidity_buffer_size": len(s.liquidityBuffer),
		"reserve_buffer_size":   len(s.reserveBuffer),
		"max_buffer_size":       s.config.BufferSize,
		"last_flush":            s.lastFlush,
	}
}
