package influxdb

import (
	"context"
	"fmt"
	"log"

	"unified-tx-parser/pkg/core"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
)

// InfluxDBConfig InfluxDB配置
type InfluxDBConfig struct {
	URL               string `yaml:"url"`                // InfluxDB服务器地址
	Token             string `yaml:"token"`              // 认证令牌
	Org               string `yaml:"org"`                // 组织名称
	Bucket            string `yaml:"bucket"`             // 存储桶名称
	BatchSize         int    `yaml:"batch_size"`         // 批量写入大小
	FlushTime         int    `yaml:"flush_time"`         // 刷新时间(秒)
	Precision         string `yaml:"precision"`          // 时间精度
	MaxRetries        int    `yaml:"max_retries"`        // 最大重试次数
	RetryInterval     int    `yaml:"retry_interval"`     // 重试间隔(秒)
	WriteTimeout      int    `yaml:"write_timeout"`      // 写入超时(秒)
	MaxConcurrency    int    `yaml:"max_concurrency"`    // 最大并发数
	BufferSize        int    `yaml:"buffer_size"`        // 缓冲区大小
	EnableCompression bool   `yaml:"enable_compression"` // 启用压缩
}

// SimpleInfluxDBStorage 简化的InfluxDB存储引擎
type SimpleInfluxDBStorage struct {
	config   *InfluxDBConfig
	client   influxdb2.Client
	writeAPI api.WriteAPI
	queryAPI api.QueryAPI
	ctx      context.Context
}

// NewSimpleInfluxDBStorage 创建简化的InfluxDB存储引擎
func NewSimpleInfluxDBStorage(config *InfluxDBConfig) (*SimpleInfluxDBStorage, error) {
	if config == nil {
		return nil, fmt.Errorf("InfluxDB配置不能为空")
	}

	// 设置默认值
	if config.BatchSize == 0 {
		config.BatchSize = 1000
	}
	if config.FlushTime == 0 {
		config.FlushTime = 10
	}
	if config.Precision == "" {
		config.Precision = "ms"
	}

	// 创建InfluxDB客户端
	client := influxdb2.NewClientWithOptions(
		config.URL,
		config.Token,
		influxdb2.DefaultOptions().
			SetBatchSize(uint(config.BatchSize)).
			SetFlushInterval(uint(config.FlushTime*1000)), // 转换为毫秒
	)

	// 测试连接
	ctx := context.Background()
	health, err := client.Health(ctx)
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("连接InfluxDB失败: %w", err)
	}

	if health.Status != "pass" {
		client.Close()
		return nil, fmt.Errorf("InfluxDB健康检查失败: %s", health.Message)
	}

	// 创建写入和查询API
	writeAPI := client.WriteAPI(config.Org, config.Bucket)
	queryAPI := client.QueryAPI(config.Org)

	storage := &SimpleInfluxDBStorage{
		config:   config,
		client:   client,
		writeAPI: writeAPI,
		queryAPI: queryAPI,
		ctx:      ctx,
	}

	log.Printf("✅ InfluxDB存储引擎初始化成功: %s/%s", config.URL, config.Bucket)
	return storage, nil
}

// StoreTransactions 存储交易数据
func (s *SimpleInfluxDBStorage) StoreTransactions(ctx context.Context, transactions []core.UnifiedTransaction) error {
	if len(transactions) == 0 {
		return nil
	}

	// 批量写入交易数据
	for _, tx := range transactions {
		point := influxdb2.NewPoint(
			"transactions",
			map[string]string{
				"chain_type":   string(tx.ChainType),
				"tx_hash":      tx.TxHash,
				"from_address": tx.FromAddress,
				"to_address":   tx.ToAddress,
			},
			map[string]interface{}{
				"block_number": tx.BlockNumber.String(),
				"value":        tx.Value.String(),
				"gas_used":     tx.GasUsed.String(),
				"gas_price":    tx.GasPrice.String(),
				"status":       string(tx.Status),
			},
			tx.Timestamp,
		)
		s.writeAPI.WritePoint(point)
	}

	// 强制刷新
	s.writeAPI.Flush()

	log.Printf("📊 InfluxDB存储交易: %d 条", len(transactions))
	return nil
}

// StoreBusinessEvents 存储业务事件数据
func (s *SimpleInfluxDBStorage) StoreBusinessEvents(ctx context.Context, events []core.BusinessEvent) error {
	if len(events) == 0 {
		return nil
	}

	// 批量写入事件数据
	for _, event := range events {
		tags := map[string]string{
			"chain_type": string(event.ChainType),
			"tx_hash":    event.TxHash,
			"event_type": string(event.EventType),
			"protocol":   event.Protocol,
		}

		fields := map[string]interface{}{
			"event_id": event.EventID,
		}

		// 根据事件类型添加特定字段
		if event.EventType == core.EventTypeSwap {
			if swapData, ok := event.Data.(*core.SwapEventData); ok {
				tags["pool_id"] = swapData.PoolID
				tags["token_in"] = swapData.TokenIn
				tags["token_out"] = swapData.TokenOut
				fields["amount_in"] = swapData.AmountIn.String()
				fields["amount_out"] = swapData.AmountOut.String()
				fields["price"] = swapData.Price
			}
		}

		point := influxdb2.NewPoint(
			"business_events",
			tags,
			fields,
			event.Timestamp,
		)
		s.writeAPI.WritePoint(point)
	}

	// 强制刷新
	s.writeAPI.Flush()

	log.Printf("📊 InfluxDB存储事件: %d 条", len(events))
	return nil
}

// GetTransactionsByHash 根据哈希获取多个交易
func (s *SimpleInfluxDBStorage) GetTransactionsByHash(ctx context.Context, hashes []string) ([]core.UnifiedTransaction, error) {
	// 简化实现：返回空结果
	return []core.UnifiedTransaction{}, nil
}

// GetEventsByTxHash 根据交易哈希获取业务事件
func (s *SimpleInfluxDBStorage) GetEventsByTxHash(ctx context.Context, txHash string) ([]core.BusinessEvent, error) {
	// 简化实现：返回空结果
	return []core.BusinessEvent{}, nil
}

// GetEventsByType 根据事件类型获取业务事件
func (s *SimpleInfluxDBStorage) GetEventsByType(ctx context.Context, eventType core.BusinessEventType, limit int) ([]core.BusinessEvent, error) {
	// 简化实现：返回空结果
	return []core.BusinessEvent{}, nil
}

// GetStorageStats 获取存储统计信息
func (s *SimpleInfluxDBStorage) GetStorageStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	stats["storage_type"] = "influxdb"
	stats["bucket"] = s.config.Bucket
	stats["url"] = s.config.URL
	stats["status"] = "connected"
	return stats, nil
}

// HealthCheck 健康检查
func (s *SimpleInfluxDBStorage) HealthCheck(ctx context.Context) error {
	health, err := s.client.Health(ctx)
	if err != nil {
		return fmt.Errorf("InfluxDB健康检查失败: %w", err)
	}

	if health.Status != "pass" {
		return fmt.Errorf("InfluxDB健康检查失败: %s", health.Message)
	}

	return nil
}

// Close 关闭存储引擎
func (s *SimpleInfluxDBStorage) Close() error {
	if s.writeAPI != nil {
		s.writeAPI.Flush()
	}
	if s.client != nil {
		s.client.Close()
	}
	log.Printf("🔒 InfluxDB存储引擎已关闭")
	return nil
}

// NewInfluxDBStorage 创建InfluxDB存储引擎（使用简化版本）
func NewInfluxDBStorage(config *InfluxDBConfig) (core.StorageEngine, error) {
	return NewSimpleInfluxDBStorage(config)
}
