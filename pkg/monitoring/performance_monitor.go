package monitoring

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"

	"unified-tx-parser/pkg/core"
)

// EnhancedPerformanceMonitor 增强的性能监控器
type EnhancedPerformanceMonitor struct {
	// 基础配置
	config *MonitorConfig
	
	// 指标收集器
	collectors map[string]MetricCollector
	
	// 实时指标
	realTimeMetrics *RealTimeMetrics
	
	// 历史数据
	historicalData *HistoricalData
	
	// 告警系统
	alertManager *AlertManager
	
	// 控制和同步
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex
	
	// 状态
	running bool
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	CollectionInterval time.Duration `yaml:"collection_interval"`
	HistoryRetention   time.Duration `yaml:"history_retention"`
	AlertThresholds    map[string]float64 `yaml:"alert_thresholds"`
	EnableCPUMonitor   bool `yaml:"enable_cpu_monitor"`
	EnableMemoryMonitor bool `yaml:"enable_memory_monitor"`
	EnableNetworkMonitor bool `yaml:"enable_network_monitor"`
	LogLevel           string `yaml:"log_level"`
	MetricsPort        int `yaml:"metrics_port"`
}

// MetricCollector 指标收集器接口
type MetricCollector interface {
	GetName() string
	Collect(ctx context.Context) (map[string]interface{}, error)
	GetMetricTypes() []string
}

// RealTimeMetrics 实时指标
type RealTimeMetrics struct {
	// 系统指标
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage uint64  `json:"memory_usage_mb"`
	GoroutineCount int  `json:"goroutine_count"`
	
	// 业务指标
	TPS            float64 `json:"transactions_per_second"`
	EventsPerSec   float64 `json:"events_per_second"`
	ProcessingLatency time.Duration `json:"processing_latency_ms"`
	ErrorRate      float64 `json:"error_rate"`
	
	// 存储指标
	StorageWriteRate float64 `json:"storage_write_rate"`
	StorageLatency   time.Duration `json:"storage_latency_ms"`
	CacheHitRate     float64 `json:"cache_hit_rate"`
	
	// 网络指标
	NetworkLatency   time.Duration `json:"network_latency_ms"`
	RequestRate      float64 `json:"request_rate"`
	
	// 时间戳
	Timestamp time.Time `json:"timestamp"`
	
	mu sync.RWMutex
}

// HistoricalData 历史数据
type HistoricalData struct {
	metrics map[string][]TimeSeriesPoint
	maxSize int
	mu      sync.RWMutex
}

// TimeSeriesPoint 时间序列数据点
type TimeSeriesPoint struct {
	Timestamp time.Time   `json:"timestamp"`
	Value     interface{} `json:"value"`
}

// AlertManager 告警管理器
type AlertManager struct {
	thresholds map[string]float64
	alerts     []Alert
	handlers   []AlertHandler
	mu         sync.RWMutex
}

// Alert 告警
type Alert struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Level       string    `json:"level"`
	Message     string    `json:"message"`
	Metric      string    `json:"metric"`
	Value       float64   `json:"value"`
	Threshold   float64   `json:"threshold"`
	Timestamp   time.Time `json:"timestamp"`
	Resolved    bool      `json:"resolved"`
	ResolvedAt  *time.Time `json:"resolved_at,omitempty"`
}

// AlertHandler 告警处理器接口
type AlertHandler interface {
	HandleAlert(alert Alert) error
	GetName() string
}

// NewEnhancedPerformanceMonitor 创建增强的性能监控器
func NewEnhancedPerformanceMonitor(config *MonitorConfig) *EnhancedPerformanceMonitor {
	if config == nil {
		config = getDefaultConfig()
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	monitor := &EnhancedPerformanceMonitor{
		config:          config,
		collectors:      make(map[string]MetricCollector),
		realTimeMetrics: &RealTimeMetrics{},
		historicalData:  newHistoricalData(1000), // 保留1000个数据点
		alertManager:    newAlertManager(config.AlertThresholds),
		ctx:             ctx,
		cancel:          cancel,
	}
	
	// 注册默认收集器
	monitor.registerDefaultCollectors()
	
	return monitor
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *MonitorConfig {
	return &MonitorConfig{
		CollectionInterval:   time.Second * 10,
		HistoryRetention:     time.Hour * 24,
		EnableCPUMonitor:     true,
		EnableMemoryMonitor:  true,
		EnableNetworkMonitor: true,
		LogLevel:            "info",
		MetricsPort:         8080,
		AlertThresholds: map[string]float64{
			"cpu_usage":      80.0,
			"memory_usage":   85.0,
			"error_rate":     5.0,
			"processing_latency": 5000.0, // 5秒
		},
	}
}

// newHistoricalData 创建历史数据存储
func newHistoricalData(maxSize int) *HistoricalData {
	return &HistoricalData{
		metrics: make(map[string][]TimeSeriesPoint),
		maxSize: maxSize,
	}
}

// newAlertManager 创建告警管理器
func newAlertManager(thresholds map[string]float64) *AlertManager {
	return &AlertManager{
		thresholds: thresholds,
		alerts:     make([]Alert, 0),
		handlers:   make([]AlertHandler, 0),
	}
}

// Start 启动性能监控
func (m *EnhancedPerformanceMonitor) Start() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.running {
		return fmt.Errorf("性能监控器已经在运行")
	}
	
	m.running = true
	
	// 启动指标收集协程
	m.wg.Add(1)
	go m.collectMetrics()
	
	// 启动告警检查协程
	m.wg.Add(1)
	go m.checkAlerts()
	
	// 启动历史数据清理协程
	m.wg.Add(1)
	go m.cleanupHistoricalData()
	
	log.Printf("✅ 增强的性能监控器已启动")
	return nil
}

// Stop 停止性能监控
func (m *EnhancedPerformanceMonitor) Stop() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if !m.running {
		return
	}
	
	m.running = false
	m.cancel()
	m.wg.Wait()
	
	log.Printf("🛑 增强的性能监控器已停止")
}

// RegisterCollector 注册指标收集器
func (m *EnhancedPerformanceMonitor) RegisterCollector(collector MetricCollector) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.collectors[collector.GetName()] = collector
	log.Printf("✅ 注册指标收集器: %s", collector.GetName())
}

// RegisterAlertHandler 注册告警处理器
func (m *EnhancedPerformanceMonitor) RegisterAlertHandler(handler AlertHandler) {
	m.alertManager.mu.Lock()
	defer m.alertManager.mu.Unlock()
	
	m.alertManager.handlers = append(m.alertManager.handlers, handler)
	log.Printf("✅ 注册告警处理器: %s", handler.GetName())
}

// GetRealTimeMetrics 获取实时指标
func (m *EnhancedPerformanceMonitor) GetRealTimeMetrics() *RealTimeMetrics {
	m.realTimeMetrics.mu.RLock()
	defer m.realTimeMetrics.mu.RUnlock()
	
	// 返回副本
	metrics := *m.realTimeMetrics
	return &metrics
}

// GetHistoricalMetrics 获取历史指标
func (m *EnhancedPerformanceMonitor) GetHistoricalMetrics(metricName string, duration time.Duration) []TimeSeriesPoint {
	m.historicalData.mu.RLock()
	defer m.historicalData.mu.RUnlock()
	
	points, exists := m.historicalData.metrics[metricName]
	if !exists {
		return []TimeSeriesPoint{}
	}
	
	// 过滤指定时间范围内的数据
	cutoff := time.Now().Add(-duration)
	var filtered []TimeSeriesPoint
	
	for _, point := range points {
		if point.Timestamp.After(cutoff) {
			filtered = append(filtered, point)
		}
	}
	
	return filtered
}

// GetActiveAlerts 获取活跃告警
func (m *EnhancedPerformanceMonitor) GetActiveAlerts() []Alert {
	m.alertManager.mu.RLock()
	defer m.alertManager.mu.RUnlock()
	
	var activeAlerts []Alert
	for _, alert := range m.alertManager.alerts {
		if !alert.Resolved {
			activeAlerts = append(activeAlerts, alert)
		}
	}
	
	return activeAlerts
}

// RecordBusinessMetric 记录业务指标
func (m *EnhancedPerformanceMonitor) RecordBusinessMetric(metricName string, value interface{}) {
	m.historicalData.mu.Lock()
	defer m.historicalData.mu.Unlock()
	
	point := TimeSeriesPoint{
		Timestamp: time.Now(),
		Value:     value,
	}
	
	if _, exists := m.historicalData.metrics[metricName]; !exists {
		m.historicalData.metrics[metricName] = make([]TimeSeriesPoint, 0)
	}
	
	m.historicalData.metrics[metricName] = append(m.historicalData.metrics[metricName], point)
	
	// 限制历史数据大小
	if len(m.historicalData.metrics[metricName]) > m.historicalData.maxSize {
		m.historicalData.metrics[metricName] = m.historicalData.metrics[metricName][1:]
	}
}

// collectMetrics 收集指标协程
func (m *EnhancedPerformanceMonitor) collectMetrics() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(m.config.CollectionInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.performCollection()
		}
	}
}

// performCollection 执行指标收集
func (m *EnhancedPerformanceMonitor) performCollection() {
	// 收集系统指标
	m.collectSystemMetrics()
	
	// 收集自定义指标
	for name, collector := range m.collectors {
		metrics, err := collector.Collect(m.ctx)
		if err != nil {
			log.Printf("⚠️ 收集器 %s 收集失败: %v", name, err)
			continue
		}
		
		// 存储到历史数据
		for metricName, value := range metrics {
			m.RecordBusinessMetric(fmt.Sprintf("%s.%s", name, metricName), value)
		}
	}
}

// collectSystemMetrics 收集系统指标
func (m *EnhancedPerformanceMonitor) collectSystemMetrics() {
	m.realTimeMetrics.mu.Lock()
	defer m.realTimeMetrics.mu.Unlock()
	
	// 内存指标
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	m.realTimeMetrics.MemoryUsage = memStats.Alloc / 1024 / 1024 // MB
	
	// Goroutine数量
	m.realTimeMetrics.GoroutineCount = runtime.NumGoroutine()
	
	// 更新时间戳
	m.realTimeMetrics.Timestamp = time.Now()
	
	// 记录到历史数据
	m.RecordBusinessMetric("system.memory_usage", m.realTimeMetrics.MemoryUsage)
	m.RecordBusinessMetric("system.goroutine_count", m.realTimeMetrics.GoroutineCount)
}

// checkAlerts 检查告警协程
func (m *EnhancedPerformanceMonitor) checkAlerts() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(time.Second * 30) // 每30秒检查一次
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.performAlertCheck()
		}
	}
}

// performAlertCheck 执行告警检查
func (m *EnhancedPerformanceMonitor) performAlertCheck() {
	metrics := m.GetRealTimeMetrics()
	
	// 检查各项指标
	m.checkMetricThreshold("memory_usage", float64(metrics.MemoryUsage))
	m.checkMetricThreshold("error_rate", metrics.ErrorRate)
	m.checkMetricThreshold("processing_latency", float64(metrics.ProcessingLatency.Milliseconds()))
}

// checkMetricThreshold 检查指标阈值
func (m *EnhancedPerformanceMonitor) checkMetricThreshold(metricName string, value float64) {
	threshold, exists := m.alertManager.thresholds[metricName]
	if !exists {
		return
	}
	
	if value > threshold {
		alert := Alert{
			ID:        fmt.Sprintf("%s_%d", metricName, time.Now().Unix()),
			Type:      "threshold",
			Level:     "warning",
			Message:   fmt.Sprintf("%s 超过阈值: %.2f > %.2f", metricName, value, threshold),
			Metric:    metricName,
			Value:     value,
			Threshold: threshold,
			Timestamp: time.Now(),
			Resolved:  false,
		}
		
		m.triggerAlert(alert)
	}
}

// triggerAlert 触发告警
func (m *EnhancedPerformanceMonitor) triggerAlert(alert Alert) {
	m.alertManager.mu.Lock()
	m.alertManager.alerts = append(m.alertManager.alerts, alert)
	m.alertManager.mu.Unlock()
	
	// 通知所有告警处理器
	for _, handler := range m.alertManager.handlers {
		if err := handler.HandleAlert(alert); err != nil {
			log.Printf("⚠️ 告警处理器 %s 处理失败: %v", handler.GetName(), err)
		}
	}
	
	log.Printf("🚨 触发告警: %s", alert.Message)
}

// cleanupHistoricalData 清理历史数据协程
func (m *EnhancedPerformanceMonitor) cleanupHistoricalData() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.performHistoricalDataCleanup()
		}
	}
}

// performHistoricalDataCleanup 执行历史数据清理
func (m *EnhancedPerformanceMonitor) performHistoricalDataCleanup() {
	m.historicalData.mu.Lock()
	defer m.historicalData.mu.Unlock()
	
	cutoff := time.Now().Add(-m.config.HistoryRetention)
	
	for metricName, points := range m.historicalData.metrics {
		var filtered []TimeSeriesPoint
		for _, point := range points {
			if point.Timestamp.After(cutoff) {
				filtered = append(filtered, point)
			}
		}
		m.historicalData.metrics[metricName] = filtered
	}
}

// registerDefaultCollectors 注册默认收集器
func (m *EnhancedPerformanceMonitor) registerDefaultCollectors() {
	// 注册系统收集器
	if m.config.EnableCPUMonitor {
		m.RegisterCollector(NewCPUCollector())
	}
	
	if m.config.EnableMemoryMonitor {
		m.RegisterCollector(NewMemoryCollector())
	}
	
	if m.config.EnableNetworkMonitor {
		m.RegisterCollector(NewNetworkCollector())
	}
}
