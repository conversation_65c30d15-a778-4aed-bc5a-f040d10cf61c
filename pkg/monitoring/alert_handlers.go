package monitoring

import (
	"fmt"
	"log"
	"os"
	"time"
)

// LogAlertHandler 日志告警处理器
type LogAlertHandler struct {
	logLevel string
}

// EmailAlertHandler 邮件告警处理器
type EmailAlertHandler struct {
	smtpServer   string
	smtpPort     int
	username     string
	password     string
	recipients   []string
	enabled      bool
}

// WebhookAlertHandler Webhook告警处理器
type WebhookAlertHandler struct {
	webhookURL string
	timeout    time.Duration
	enabled    bool
}

// SlackAlertHandler Slack告警处理器
type SlackAlertHandler struct {
	webhookURL string
	channel    string
	username   string
	enabled    bool
}

// NewLogAlertHandler 创建日志告警处理器
func NewLogAlertHandler(logLevel string) *LogAlertHandler {
	return &LogAlertHandler{
		logLevel: logLevel,
	}
}

// GetName 获取处理器名称
func (h *LogAlertHandler) GetName() string {
	return "log"
}

// HandleAlert 处理告警
func (h *LogAlertHandler) HandleAlert(alert Alert) error {
	logMessage := fmt.Sprintf("[ALERT] %s | %s | %s: %.2f (threshold: %.2f) | %s",
		alert.Level,
		alert.Type,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
	)
	
	switch alert.Level {
	case "critical":
		log.Printf("🚨 CRITICAL: %s", logMessage)
	case "warning":
		log.Printf("⚠️ WARNING: %s", logMessage)
	case "info":
		log.Printf("ℹ️ INFO: %s", logMessage)
	default:
		log.Printf("📊 ALERT: %s", logMessage)
	}
	
	return nil
}

// NewEmailAlertHandler 创建邮件告警处理器
func NewEmailAlertHandler(smtpServer string, smtpPort int, username, password string, recipients []string) *EmailAlertHandler {
	return &EmailAlertHandler{
		smtpServer: smtpServer,
		smtpPort:   smtpPort,
		username:   username,
		password:   password,
		recipients: recipients,
		enabled:    len(recipients) > 0 && smtpServer != "",
	}
}

// GetName 获取处理器名称
func (h *EmailAlertHandler) GetName() string {
	return "email"
}

// HandleAlert 处理告警
func (h *EmailAlertHandler) HandleAlert(alert Alert) error {
	if !h.enabled {
		return fmt.Errorf("邮件告警处理器未启用")
	}
	
	subject := fmt.Sprintf("[Chain Parse Service] %s Alert: %s", alert.Level, alert.Metric)
	body := h.formatEmailBody(alert)
	
	// 这里应该实现实际的邮件发送逻辑
	log.Printf("📧 发送邮件告警: %s -> %v", subject, h.recipients)
	log.Printf("📧 邮件内容: %s", body)
	
	return nil
}

// formatEmailBody 格式化邮件内容
func (h *EmailAlertHandler) formatEmailBody(alert Alert) string {
	return fmt.Sprintf(`
Chain Parse Service Alert

Alert ID: %s
Type: %s
Level: %s
Metric: %s
Current Value: %.2f
Threshold: %.2f
Message: %s
Timestamp: %s

Please check the system status and take appropriate action if necessary.

Best regards,
Chain Parse Service Monitoring System
`,
		alert.ID,
		alert.Type,
		alert.Level,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
		alert.Timestamp.Format("2006-01-02 15:04:05"),
	)
}

// NewWebhookAlertHandler 创建Webhook告警处理器
func NewWebhookAlertHandler(webhookURL string, timeout time.Duration) *WebhookAlertHandler {
	return &WebhookAlertHandler{
		webhookURL: webhookURL,
		timeout:    timeout,
		enabled:    webhookURL != "",
	}
}

// GetName 获取处理器名称
func (h *WebhookAlertHandler) GetName() string {
	return "webhook"
}

// HandleAlert 处理告警
func (h *WebhookAlertHandler) HandleAlert(alert Alert) error {
	if !h.enabled {
		return fmt.Errorf("Webhook告警处理器未启用")
	}
	
	payload := h.formatWebhookPayload(alert)
	
	// 这里应该实现实际的HTTP请求发送逻辑
	log.Printf("🔗 发送Webhook告警: %s", h.webhookURL)
	log.Printf("🔗 Payload: %s", payload)
	
	return nil
}

// formatWebhookPayload 格式化Webhook载荷
func (h *WebhookAlertHandler) formatWebhookPayload(alert Alert) string {
	return fmt.Sprintf(`{
	"alert_id": "%s",
	"type": "%s",
	"level": "%s",
	"metric": "%s",
	"value": %.2f,
	"threshold": %.2f,
	"message": "%s",
	"timestamp": "%s",
	"service": "chain-parse-service"
}`,
		alert.ID,
		alert.Type,
		alert.Level,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
		alert.Timestamp.Format(time.RFC3339),
	)
}

// NewSlackAlertHandler 创建Slack告警处理器
func NewSlackAlertHandler(webhookURL, channel, username string) *SlackAlertHandler {
	return &SlackAlertHandler{
		webhookURL: webhookURL,
		channel:    channel,
		username:   username,
		enabled:    webhookURL != "",
	}
}

// GetName 获取处理器名称
func (h *SlackAlertHandler) GetName() string {
	return "slack"
}

// HandleAlert 处理告警
func (h *SlackAlertHandler) HandleAlert(alert Alert) error {
	if !h.enabled {
		return fmt.Errorf("Slack告警处理器未启用")
	}
	
	payload := h.formatSlackPayload(alert)
	
	// 这里应该实现实际的Slack消息发送逻辑
	log.Printf("💬 发送Slack告警: %s", h.channel)
	log.Printf("💬 Payload: %s", payload)
	
	return nil
}

// formatSlackPayload 格式化Slack载荷
func (h *SlackAlertHandler) formatSlackPayload(alert Alert) string {
	emoji := h.getAlertEmoji(alert.Level)
	color := h.getAlertColor(alert.Level)
	
	return fmt.Sprintf(`{
	"channel": "%s",
	"username": "%s",
	"icon_emoji": "%s",
	"attachments": [
		{
			"color": "%s",
			"title": "Chain Parse Service Alert",
			"fields": [
				{
					"title": "Alert Type",
					"value": "%s",
					"short": true
				},
				{
					"title": "Level",
					"value": "%s",
					"short": true
				},
				{
					"title": "Metric",
					"value": "%s",
					"short": true
				},
				{
					"title": "Value",
					"value": "%.2f (threshold: %.2f)",
					"short": true
				},
				{
					"title": "Message",
					"value": "%s",
					"short": false
				},
				{
					"title": "Timestamp",
					"value": "%s",
					"short": true
				}
			]
		}
	]
}`,
		h.channel,
		h.username,
		emoji,
		color,
		alert.Type,
		alert.Level,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
		alert.Timestamp.Format("2006-01-02 15:04:05"),
	)
}

// getAlertEmoji 获取告警表情符号
func (h *SlackAlertHandler) getAlertEmoji(level string) string {
	switch level {
	case "critical":
		return ":rotating_light:"
	case "warning":
		return ":warning:"
	case "info":
		return ":information_source:"
	default:
		return ":bell:"
	}
}

// getAlertColor 获取告警颜色
func (h *SlackAlertHandler) getAlertColor(level string) string {
	switch level {
	case "critical":
		return "danger"
	case "warning":
		return "warning"
	case "info":
		return "good"
	default:
		return "#36a64f"
	}
}

// FileAlertHandler 文件告警处理器
type FileAlertHandler struct {
	filePath string
	enabled  bool
}

// NewFileAlertHandler 创建文件告警处理器
func NewFileAlertHandler(filePath string) *FileAlertHandler {
	return &FileAlertHandler{
		filePath: filePath,
		enabled:  filePath != "",
	}
}

// GetName 获取处理器名称
func (h *FileAlertHandler) GetName() string {
	return "file"
}

// HandleAlert 处理告警
func (h *FileAlertHandler) HandleAlert(alert Alert) error {
	if !h.enabled {
		return fmt.Errorf("文件告警处理器未启用")
	}
	
	alertLine := fmt.Sprintf("[%s] %s | %s | %s: %.2f (threshold: %.2f) | %s\n",
		alert.Timestamp.Format("2006-01-02 15:04:05"),
		alert.Level,
		alert.Type,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
	)
	
	// 追加写入文件
	file, err := os.OpenFile(h.filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开告警文件失败: %w", err)
	}
	defer file.Close()
	
	if _, err := file.WriteString(alertLine); err != nil {
		return fmt.Errorf("写入告警文件失败: %w", err)
	}
	
	log.Printf("📄 告警已写入文件: %s", h.filePath)
	return nil
}

// ConsoleAlertHandler 控制台告警处理器
type ConsoleAlertHandler struct {
	colorEnabled bool
}

// NewConsoleAlertHandler 创建控制台告警处理器
func NewConsoleAlertHandler(colorEnabled bool) *ConsoleAlertHandler {
	return &ConsoleAlertHandler{
		colorEnabled: colorEnabled,
	}
}

// GetName 获取处理器名称
func (h *ConsoleAlertHandler) GetName() string {
	return "console"
}

// HandleAlert 处理告警
func (h *ConsoleAlertHandler) HandleAlert(alert Alert) error {
	if h.colorEnabled {
		h.printColoredAlert(alert)
	} else {
		h.printPlainAlert(alert)
	}
	
	return nil
}

// printColoredAlert 打印彩色告警
func (h *ConsoleAlertHandler) printColoredAlert(alert Alert) {
	const (
		ColorReset  = "\033[0m"
		ColorRed    = "\033[31m"
		ColorYellow = "\033[33m"
		ColorBlue   = "\033[34m"
		ColorGreen  = "\033[32m"
	)
	
	var color string
	switch alert.Level {
	case "critical":
		color = ColorRed
	case "warning":
		color = ColorYellow
	case "info":
		color = ColorBlue
	default:
		color = ColorGreen
	}
	
	fmt.Printf("%s[ALERT] %s | %s | %s: %.2f (threshold: %.2f) | %s%s\n",
		color,
		alert.Level,
		alert.Type,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
		ColorReset,
	)
}

// printPlainAlert 打印普通告警
func (h *ConsoleAlertHandler) printPlainAlert(alert Alert) {
	fmt.Printf("[ALERT] %s | %s | %s: %.2f (threshold: %.2f) | %s\n",
		alert.Level,
		alert.Type,
		alert.Metric,
		alert.Value,
		alert.Threshold,
		alert.Message,
	)
}
