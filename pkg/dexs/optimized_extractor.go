package dex

import (
	"context"
	"crypto/md5"
	"fmt"
	"log"
	"runtime"
	"strings"
	"sync"
	"time"

	"unified-tx-parser/pkg/core"
)

// OptimizedDEXExtractor 优化的DEX事件提取器
type OptimizedDEXExtractor struct {
	// 基础组件
	protocolHandlers map[string]ProtocolHandler
	supportedChains  []core.ChainType

	// 优化组件
	cache           *EventCache
	filterOptimizer *FilterOptimizer
	poolManager     *WorkerPoolManager

	// 统计信息
	stats *ExtractorStats
	mu    sync.RWMutex
}

// EventCache 事件缓存
type EventCache struct {
	cache     map[string][]core.BusinessEvent
	expiry    map[string]time.Time
	maxSize   int
	ttl       time.Duration
	hitCount  int64
	missCount int64
	mu        sync.RWMutex
}

// FilterOptimizer 过滤优化器
type FilterOptimizer struct {
	// 地址白名单缓存
	knownDEXAddresses map[string]bool

	// 协议特征缓存
	protocolSignatures map[string][]string

	// 快速过滤规则
	quickFilters []QuickFilter

	mu sync.RWMutex
}

// QuickFilter 快速过滤器
type QuickFilter struct {
	Name      string
	Condition func(*core.UnifiedTransaction) bool
	Priority  int
}

// WorkerPoolManager 工作池管理器
type WorkerPoolManager struct {
	workerCount int
	taskQueue   chan ExtractionTask
	resultQueue chan ExtractionResult
	workers     []*Worker
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
}

// ExtractionTask 提取任务
type ExtractionTask struct {
	Transaction *core.UnifiedTransaction
	Handlers    []ProtocolHandler
	TaskID      string
}

// ExtractionResult 提取结果
type ExtractionResult struct {
	TaskID   string
	Events   []core.BusinessEvent
	Error    error
	Duration time.Duration
}

// Worker 工作协程
type Worker struct {
	id          int
	taskQueue   chan ExtractionTask
	resultQueue chan ExtractionResult
	quit        chan bool
}

// ExtractorStats 提取器统计信息
type ExtractorStats struct {
	TotalTransactions     int64         `json:"total_transactions"`
	ProcessedTransactions int64         `json:"processed_transactions"`
	ExtractedEvents       int64         `json:"extracted_events"`
	CacheHits             int64         `json:"cache_hits"`
	CacheMisses           int64         `json:"cache_misses"`
	AvgProcessingTime     time.Duration `json:"avg_processing_time"`
	FilteredOut           int64         `json:"filtered_out"`
	ErrorCount            int64         `json:"error_count"`
	mu                    sync.RWMutex
}

// NewOptimizedDEXExtractor 创建优化的DEX提取器
func NewOptimizedDEXExtractor(config map[string]interface{}) *OptimizedDEXExtractor {
	extractor := &OptimizedDEXExtractor{
		protocolHandlers: make(map[string]ProtocolHandler),
		supportedChains:  make([]core.ChainType, 0),
		cache:            newEventCache(10000, time.Minute*5), // 5分钟TTL
		filterOptimizer:  newFilterOptimizer(),
		stats:            &ExtractorStats{},
	}

	// 初始化工作池
	workerCount := runtime.NumCPU() * 2
	extractor.poolManager = newWorkerPoolManager(workerCount)

	// 注册协议处理器
	extractor.registerOptimizedHandlers(config)

	// 初始化过滤优化器
	extractor.initializeFilterOptimizer()

	log.Printf("✅ 优化的DEX提取器已创建 (工作协程: %d)", workerCount)
	return extractor
}

// newEventCache 创建事件缓存
func newEventCache(maxSize int, ttl time.Duration) *EventCache {
	cache := &EventCache{
		cache:   make(map[string][]core.BusinessEvent),
		expiry:  make(map[string]time.Time),
		maxSize: maxSize,
		ttl:     ttl,
	}

	// 启动清理协程
	go cache.cleanup()

	return cache
}

// newFilterOptimizer 创建过滤优化器
func newFilterOptimizer() *FilterOptimizer {
	optimizer := &FilterOptimizer{
		knownDEXAddresses:  make(map[string]bool),
		protocolSignatures: make(map[string][]string),
		quickFilters:       make([]QuickFilter, 0),
	}

	// 添加快速过滤规则
	optimizer.addQuickFilters()

	return optimizer
}

// newWorkerPoolManager 创建工作池管理器
func newWorkerPoolManager(workerCount int) *WorkerPoolManager {
	ctx, cancel := context.WithCancel(context.Background())

	manager := &WorkerPoolManager{
		workerCount: workerCount,
		taskQueue:   make(chan ExtractionTask, workerCount*10),
		resultQueue: make(chan ExtractionResult, workerCount*10),
		workers:     make([]*Worker, workerCount),
		ctx:         ctx,
		cancel:      cancel,
	}

	// 启动工作协程
	for i := 0; i < workerCount; i++ {
		worker := &Worker{
			id:          i,
			taskQueue:   manager.taskQueue,
			resultQueue: manager.resultQueue,
			quit:        make(chan bool),
		}
		manager.workers[i] = worker

		manager.wg.Add(1)
		go worker.start(&manager.wg)
	}

	return manager
}

// registerOptimizedHandlers 注册优化的协议处理器
func (e *OptimizedDEXExtractor) registerOptimizedHandlers(config map[string]interface{}) {
	registeredProtocols := make([]string, 0)

	// 检查每个协议是否在配置中启用
	for protocolName := range config {
		var handler ProtocolHandler

		switch strings.ToLower(protocolName) {
		case "uniswap":
			handler = NewUniswapHandler()
		case "bluefin":
			handler = NewBluefinHandler()
		case "jupiter":
			handler = NewJupiterHandler()
		case "pancakeswap":
			handler = NewPancakeSwapHandler()
		default:
			continue
		}

		if handler != nil {
			e.protocolHandlers[protocolName] = handler
			registeredProtocols = append(registeredProtocols, protocolName)

			// 更新支持的链类型
			for _, chainType := range handler.GetSupportedChains() {
				if !e.containsChain(chainType) {
					e.supportedChains = append(e.supportedChains, chainType)
				}
			}
		}
	}

	log.Printf("📦 已注册优化协议: %v", registeredProtocols)
}

// initializeFilterOptimizer 初始化过滤优化器
func (e *OptimizedDEXExtractor) initializeFilterOptimizer() {
	// 预加载已知的DEX地址
	e.filterOptimizer.loadKnownDEXAddresses()

	// 预加载协议特征
	e.filterOptimizer.loadProtocolSignatures(e.protocolHandlers)
}

// ExtractEvents 优化的事件提取
func (e *OptimizedDEXExtractor) ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	startTime := time.Now()

	e.stats.mu.Lock()
	e.stats.TotalTransactions++
	e.stats.mu.Unlock()

	// 快速过滤检查
	if !e.filterOptimizer.QuickFilter(tx) {
		e.stats.mu.Lock()
		e.stats.FilteredOut++
		e.stats.mu.Unlock()
		return []core.BusinessEvent{}, nil
	}

	// 检查缓存
	cacheKey := e.generateCacheKey(tx)
	if cachedEvents := e.cache.Get(cacheKey); cachedEvents != nil {
		e.stats.mu.Lock()
		e.stats.CacheHits++
		e.stats.mu.Unlock()
		return cachedEvents, nil
	}

	e.stats.mu.Lock()
	e.stats.CacheMisses++
	e.stats.mu.Unlock()

	// 使用工作池提取事件
	events, err := e.extractEventsWithPool(ctx, tx)
	if err != nil {
		e.stats.mu.Lock()
		e.stats.ErrorCount++
		e.stats.mu.Unlock()
		return nil, err
	}

	// 缓存结果
	if len(events) > 0 {
		e.cache.Set(cacheKey, events)
	}

	// 更新统计信息
	duration := time.Since(startTime)
	e.updateStats(len(events), duration)

	return events, nil
}

// extractEventsWithPool 使用工作池提取事件
func (e *OptimizedDEXExtractor) extractEventsWithPool(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	// 筛选相关的协议处理器
	relevantHandlers := e.filterOptimizer.GetRelevantHandlers(tx, e.protocolHandlers)
	if len(relevantHandlers) == 0 {
		return []core.BusinessEvent{}, nil
	}

	// 创建提取任务
	taskID := e.generateTaskID(tx)
	task := ExtractionTask{
		Transaction: tx,
		Handlers:    relevantHandlers,
		TaskID:      taskID,
	}

	// 发送任务到工作池
	select {
	case e.poolManager.taskQueue <- task:
		// 任务已发送
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		// 队列满，直接处理
		return e.extractEventsDirectly(ctx, tx, relevantHandlers)
	}

	// 等待结果
	select {
	case result := <-e.poolManager.resultQueue:
		if result.TaskID == taskID {
			return result.Events, result.Error
		}
		// 不是我们的结果，放回队列
		e.poolManager.resultQueue <- result
		return e.extractEventsDirectly(ctx, tx, relevantHandlers)
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(time.Second * 5):
		// 超时，直接处理
		return e.extractEventsDirectly(ctx, tx, relevantHandlers)
	}
}

// extractEventsDirectly 直接提取事件
func (e *OptimizedDEXExtractor) extractEventsDirectly(ctx context.Context, tx *core.UnifiedTransaction, handlers []ProtocolHandler) ([]core.BusinessEvent, error) {
	var allEvents []core.BusinessEvent

	for _, handler := range handlers {
		if !handler.SupportsTransaction(tx) {
			continue
		}

		events, err := handler.ExtractEvents(ctx, tx)
		if err != nil {
			log.Printf("⚠️ %s-%s: 提取失败 - %v", tx.ChainType, handler.GetProtocolName(), err)
			continue
		}

		allEvents = append(allEvents, events...)
	}

	return allEvents, nil
}

// SupportsTransaction 检查是否支持该交易
func (e *OptimizedDEXExtractor) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	return e.filterOptimizer.QuickFilter(tx)
}

// GetSupportedProtocols 获取支持的协议
func (e *OptimizedDEXExtractor) GetSupportedProtocols() []string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	protocols := make([]string, 0, len(e.protocolHandlers))
	for protocol := range e.protocolHandlers {
		protocols = append(protocols, protocol)
	}
	return protocols
}

// GetSupportedChains 获取支持的链类型
func (e *OptimizedDEXExtractor) GetSupportedChains() []core.ChainType {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.supportedChains
}

// GetStats 获取统计信息
func (e *OptimizedDEXExtractor) GetStats() map[string]interface{} {
	e.stats.mu.RLock()
	defer e.stats.mu.RUnlock()

	cacheStats := e.cache.GetStats()

	return map[string]interface{}{
		"total_transactions":     e.stats.TotalTransactions,
		"processed_transactions": e.stats.ProcessedTransactions,
		"extracted_events":       e.stats.ExtractedEvents,
		"filtered_out":           e.stats.FilteredOut,
		"error_count":            e.stats.ErrorCount,
		"avg_processing_time_ms": float64(e.stats.AvgProcessingTime.Nanoseconds()) / 1e6,
		"cache_stats":            cacheStats,
		"worker_pool_stats":      e.poolManager.GetStats(),
	}
}

// Close 关闭提取器
func (e *OptimizedDEXExtractor) Close() error {
	log.Printf("🛑 正在关闭优化的DEX提取器...")

	// 关闭工作池
	e.poolManager.Close()

	// 清理缓存
	e.cache.Clear()

	log.Printf("✅ 优化的DEX提取器已关闭")
	return nil
}

// 辅助方法
func (e *OptimizedDEXExtractor) containsChain(chainType core.ChainType) bool {
	for _, ct := range e.supportedChains {
		if ct == chainType {
			return true
		}
	}
	return false
}

func (e *OptimizedDEXExtractor) generateCacheKey(tx *core.UnifiedTransaction) string {
	data := fmt.Sprintf("%s:%s:%s", tx.ChainType, tx.TxHash, tx.ToAddress)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}

func (e *OptimizedDEXExtractor) generateTaskID(tx *core.UnifiedTransaction) string {
	return fmt.Sprintf("%s:%s:%d", tx.ChainType, tx.TxHash, time.Now().UnixNano())
}

func (e *OptimizedDEXExtractor) updateStats(eventCount int, duration time.Duration) {
	e.stats.mu.Lock()
	defer e.stats.mu.Unlock()

	e.stats.ProcessedTransactions++
	e.stats.ExtractedEvents += int64(eventCount)

	// 更新平均处理时间
	if e.stats.AvgProcessingTime == 0 {
		e.stats.AvgProcessingTime = duration
	} else {
		e.stats.AvgProcessingTime = (e.stats.AvgProcessingTime + duration) / 2
	}
}

// EventCache methods
func (c *EventCache) Get(key string) []core.BusinessEvent {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if expiry, exists := c.expiry[key]; exists {
		if time.Now().Before(expiry) {
			c.hitCount++
			return c.cache[key]
		}
		// 过期，删除
		delete(c.cache, key)
		delete(c.expiry, key)
	}

	c.missCount++
	return nil
}

func (c *EventCache) Set(key string, events []core.BusinessEvent) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 检查缓存大小
	if len(c.cache) >= c.maxSize {
		c.evictOldest()
	}

	c.cache[key] = events
	c.expiry[key] = time.Now().Add(c.ttl)
}

func (c *EventCache) evictOldest() {
	oldestKey := ""
	oldestTime := time.Now()

	for key, expiry := range c.expiry {
		if expiry.Before(oldestTime) {
			oldestTime = expiry
			oldestKey = key
		}
	}

	if oldestKey != "" {
		delete(c.cache, oldestKey)
		delete(c.expiry, oldestKey)
	}
}

func (c *EventCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		for key, expiry := range c.expiry {
			if now.After(expiry) {
				delete(c.cache, key)
				delete(c.expiry, key)
			}
		}
		c.mu.Unlock()
	}
}

func (c *EventCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.cache = make(map[string][]core.BusinessEvent)
	c.expiry = make(map[string]time.Time)
}

func (c *EventCache) GetStats() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	total := c.hitCount + c.missCount
	hitRate := 0.0
	if total > 0 {
		hitRate = float64(c.hitCount) / float64(total)
	}

	return map[string]interface{}{
		"size":        len(c.cache),
		"max_size":    c.maxSize,
		"hit_count":   c.hitCount,
		"miss_count":  c.missCount,
		"hit_rate":    hitRate,
		"ttl_seconds": c.ttl.Seconds(),
	}
}

// FilterOptimizer methods
func (f *FilterOptimizer) QuickFilter(tx *core.UnifiedTransaction) bool {
	f.mu.RLock()
	defer f.mu.RUnlock()

	// 按优先级执行快速过滤器
	for _, filter := range f.quickFilters {
		if !filter.Condition(tx) {
			return false
		}
	}

	return true
}

func (f *FilterOptimizer) GetRelevantHandlers(tx *core.UnifiedTransaction, handlers map[string]ProtocolHandler) []ProtocolHandler {
	f.mu.RLock()
	defer f.mu.RUnlock()

	var relevantHandlers []ProtocolHandler

	// 检查已知DEX地址
	if f.knownDEXAddresses[strings.ToLower(tx.ToAddress)] {
		// 这是已知的DEX地址，返回所有处理器
		for _, handler := range handlers {
			if handler.SupportsTransaction(tx) {
				relevantHandlers = append(relevantHandlers, handler)
			}
		}
		return relevantHandlers
	}

	// 基于协议特征匹配
	for protocolName, signatures := range f.protocolSignatures {
		if handler, exists := handlers[protocolName]; exists {
			if f.matchesProtocolSignature(tx, signatures) {
				relevantHandlers = append(relevantHandlers, handler)
			}
		}
	}

	// 如果没有匹配的特征，返回所有支持该链的处理器
	if len(relevantHandlers) == 0 {
		for _, handler := range handlers {
			for _, chainType := range handler.GetSupportedChains() {
				if chainType == tx.ChainType {
					relevantHandlers = append(relevantHandlers, handler)
					break
				}
			}
		}
	}

	return relevantHandlers
}

func (f *FilterOptimizer) addQuickFilters() {
	// 添加基本过滤规则
	f.quickFilters = append(f.quickFilters, QuickFilter{
		Name:     "non_zero_value",
		Priority: 1,
		Condition: func(tx *core.UnifiedTransaction) bool {
			return tx.Value != nil && tx.Value.Sign() >= 0
		},
	})

	f.quickFilters = append(f.quickFilters, QuickFilter{
		Name:     "successful_transaction",
		Priority: 2,
		Condition: func(tx *core.UnifiedTransaction) bool {
			return tx.Status == core.TransactionStatusSuccess
		},
	})

	f.quickFilters = append(f.quickFilters, QuickFilter{
		Name:     "has_to_address",
		Priority: 3,
		Condition: func(tx *core.UnifiedTransaction) bool {
			return tx.ToAddress != "" && tx.ToAddress != "0x0000000000000000000000000000000000000000"
		},
	})
}

func (f *FilterOptimizer) loadKnownDEXAddresses() {
	// 预加载已知的DEX合约地址
	knownAddresses := []string{
		// Uniswap V2
		"0x7a250d5630b4cf539739df2c5dacb4c659f2488d",
		// Uniswap V3
		"0xe592427a0aece92de3edee1f18e0157c05861564",
		// PancakeSwap
		"0x10ed43c718714eb63d5aa57b78b54704e256024e",
		// SushiSwap
		"0xd9e1ce17f2641f24ae83637ab66a2cca9c378b9f",
	}

	f.mu.Lock()
	defer f.mu.Unlock()

	for _, addr := range knownAddresses {
		f.knownDEXAddresses[strings.ToLower(addr)] = true
	}
}

func (f *FilterOptimizer) loadProtocolSignatures(handlers map[string]ProtocolHandler) {
	f.mu.Lock()
	defer f.mu.Unlock()

	// 为每个协议加载特征签名
	f.protocolSignatures["uniswap"] = []string{
		"0xa9059cbb", // transfer
		"0x095ea7b3", // approve
		"0x38ed1739", // swapExactTokensForTokens
	}

	f.protocolSignatures["pancakeswap"] = []string{
		"0xa9059cbb", // transfer
		"0x095ea7b3", // approve
		"0x38ed1739", // swapExactTokensForTokens
	}
}

func (f *FilterOptimizer) matchesProtocolSignature(tx *core.UnifiedTransaction, signatures []string) bool {
	// 简化实现：检查交易数据是否包含协议特征
	// 实际实现应该解析交易数据并检查方法签名
	return true // 暂时返回true，实际应该实现具体的匹配逻辑
}

// Worker methods
func (w *Worker) start(wg *sync.WaitGroup) {
	defer wg.Done()

	for {
		select {
		case task := <-w.taskQueue:
			result := w.processTask(task)
			w.resultQueue <- result
		case <-w.quit:
			return
		}
	}
}

func (w *Worker) processTask(task ExtractionTask) ExtractionResult {
	startTime := time.Now()

	var allEvents []core.BusinessEvent
	var lastErr error

	for _, handler := range task.Handlers {
		if !handler.SupportsTransaction(task.Transaction) {
			continue
		}

		events, err := handler.ExtractEvents(context.Background(), task.Transaction)
		if err != nil {
			lastErr = err
			continue
		}

		allEvents = append(allEvents, events...)
	}

	return ExtractionResult{
		TaskID:   task.TaskID,
		Events:   allEvents,
		Error:    lastErr,
		Duration: time.Since(startTime),
	}
}

func (w *Worker) stop() {
	w.quit <- true
}

// WorkerPoolManager methods
func (wpm *WorkerPoolManager) Close() {
	wpm.cancel()

	// 停止所有工作协程
	for _, worker := range wpm.workers {
		worker.stop()
	}

	wpm.wg.Wait()

	// 关闭通道
	close(wpm.taskQueue)
	close(wpm.resultQueue)
}

func (wpm *WorkerPoolManager) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"worker_count":      wpm.workerCount,
		"task_queue_size":   len(wpm.taskQueue),
		"result_queue_size": len(wpm.resultQueue),
		"task_queue_cap":    cap(wpm.taskQueue),
		"result_queue_cap":  cap(wpm.resultQueue),
	}
}
