package core

import (
	"fmt"
	"math/big"
	"runtime"
	"strings"
	"sync"
	"time"
)

// BatchManager methods
func (bm *BatchManager) GetOptimalBatchSize(chainType ChainType) int {
	bm.mu.RLock()
	defer bm.mu.RUnlock()
	
	if !bm.adaptiveEnabled {
		return bm.baseBatchSize
	}
	
	// 根据性能调整批处理大小
	if bm.avgProcessingTime > time.Second*10 {
		// 处理时间过长，减少批处理大小
		newSize := int(float64(bm.baseBatchSize) * 0.8)
		if newSize < bm.minBatchSize {
			newSize = bm.minBatchSize
		}
		return newSize
	} else if bm.avgProcessingTime < time.Second*2 && bm.successRate > 0.95 {
		// 处理快速且成功率高，增加批处理大小
		newSize := int(float64(bm.baseBatchSize) * 1.2)
		if newSize > bm.maxBatchSize {
			newSize = bm.maxBatchSize
		}
		return newSize
	}
	
	return bm.baseBatchSize
}

func (bm *BatchManager) UpdateStats(chainType ChainType, txCount int, processingTime time.Duration, success bool) {
	bm.mu.Lock()
	defer bm.mu.Unlock()
	
	// 更新平均处理时间
	if bm.avgProcessingTime == 0 {
		bm.avgProcessingTime = processingTime
	} else {
		bm.avgProcessingTime = (bm.avgProcessingTime + processingTime) / 2
	}
	
	// 更新成功率
	if success {
		bm.successRate = (bm.successRate*0.9 + 1.0*0.1)
	} else {
		bm.successRate = (bm.successRate*0.9 + 0.0*0.1)
	}
	
	bm.lastAdjustment = time.Now()
}

// RetryManager methods
func (rm *RetryManager) CalculateDelay(attempt int) time.Duration {
	rm.mu.RLock()
	defer rm.mu.RUnlock()
	
	delay := time.Duration(float64(rm.baseDelay) * float64(attempt) * rm.backoffFactor)
	if delay > rm.maxDelay {
		delay = rm.maxDelay
	}
	
	return delay
}

func (rm *RetryManager) IsRetryable(err error) bool {
	rm.mu.RLock()
	defer rm.mu.RUnlock()
	
	if err == nil {
		return false
	}
	
	errStr := strings.ToLower(err.Error())
	for errorType := range rm.retryableErrors {
		if strings.Contains(errStr, errorType) {
			return true
		}
	}
	
	return false
}

func (rm *RetryManager) RecordRetry() {
	rm.mu.Lock()
	defer rm.mu.Unlock()
	
	rm.totalRetries++
}

func (rm *RetryManager) RecordSuccess() {
	rm.mu.Lock()
	defer rm.mu.Unlock()
	
	rm.successfulRetries++
}

func (rm *RetryManager) RecordFailure() {
	rm.mu.Lock()
	defer rm.mu.Unlock()
	
	rm.failedRetries++
}

func (rm *RetryManager) GetStats() map[string]interface{} {
	rm.mu.RLock()
	defer rm.mu.RUnlock()
	
	return map[string]interface{}{
		"total_retries":     rm.totalRetries,
		"successful_retries": rm.successfulRetries,
		"failed_retries":    rm.failedRetries,
		"success_rate":      float64(rm.successfulRetries) / float64(rm.totalRetries),
	}
}

// MemoryManager methods
func (mm *MemoryManager) CheckMemoryAvailable() bool {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	currentMB := m.Alloc / 1024 / 1024
	mm.currentUsage = currentMB
	
	if currentMB > mm.peakUsage {
		mm.peakUsage = currentMB
	}
	
	return currentMB < uint64(float64(mm.maxMemoryUsage)*mm.gcThreshold)
}

func (mm *MemoryManager) CheckAndManage() {
	mm.mu.Lock()
	defer mm.mu.Unlock()
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	currentMB := m.Alloc / 1024 / 1024
	mm.currentUsage = currentMB
	
	if currentMB > mm.peakUsage {
		mm.peakUsage = currentMB
	}
	
	// 检查是否需要强制GC
	if currentMB > uint64(float64(mm.maxMemoryUsage)*mm.gcThreshold) {
		runtime.GC()
		mm.gcCount++
		mm.lastGCTime = time.Now()
		
		// 再次检查内存使用情况
		runtime.ReadMemStats(&m)
		newMB := m.Alloc / 1024 / 1024
		fmt.Printf("🧹 强制GC: %dMB -> %dMB\n", currentMB, newMB)
	}
}

func (mm *MemoryManager) GetStats() map[string]interface{} {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	return map[string]interface{}{
		"current_usage_mb": mm.currentUsage,
		"peak_usage_mb":    mm.peakUsage,
		"max_usage_mb":     mm.maxMemoryUsage,
		"gc_count":         mm.gcCount,
		"last_gc_time":     mm.lastGCTime,
		"gc_threshold":     mm.gcThreshold,
	}
}

// PerformanceMonitor methods
func (pm *PerformanceMonitor) CollectMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 添加内存使用情况到历史
	pm.addToHistory(&pm.memoryHistory, m.Alloc/1024/1024)
}

func (pm *PerformanceMonitor) RecordTPS(tps float64) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.addToHistory(&pm.tpsHistory, tps)
}

func (pm *PerformanceMonitor) RecordLatency(latency time.Duration) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.latencyHistory = append(pm.latencyHistory, latency)
	if len(pm.latencyHistory) > pm.historySize {
		pm.latencyHistory = pm.latencyHistory[1:]
	}
}

func (pm *PerformanceMonitor) RecordErrorRate(errorRate float64) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.addToHistory(&pm.errorRateHistory, errorRate)
}

func (pm *PerformanceMonitor) addToHistory(history *[]float64, value float64) {
	*history = append(*history, value)
	if len(*history) > pm.historySize {
		*history = (*history)[1:]
	}
}

func (pm *PerformanceMonitor) addToHistoryUint64(history *[]uint64, value uint64) {
	*history = append(*history, value)
	if len(*history) > pm.historySize {
		*history = (*history)[1:]
	}
}

func (pm *PerformanceMonitor) GetStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	stats := map[string]interface{}{
		"history_size": len(pm.tpsHistory),
	}
	
	if len(pm.tpsHistory) > 0 {
		avg := 0.0
		for _, tps := range pm.tpsHistory {
			avg += tps
		}
		stats["avg_tps"] = avg / float64(len(pm.tpsHistory))
		stats["current_tps"] = pm.tpsHistory[len(pm.tpsHistory)-1]
	}
	
	if len(pm.latencyHistory) > 0 {
		var total time.Duration
		for _, latency := range pm.latencyHistory {
			total += latency
		}
		stats["avg_latency_ms"] = float64(total.Nanoseconds()) / float64(len(pm.latencyHistory)) / 1e6
		stats["current_latency_ms"] = float64(pm.latencyHistory[len(pm.latencyHistory)-1].Nanoseconds()) / 1e6
	}
	
	if len(pm.errorRateHistory) > 0 {
		avg := 0.0
		for _, rate := range pm.errorRateHistory {
			avg += rate
		}
		stats["avg_error_rate"] = avg / float64(len(pm.errorRateHistory))
		stats["current_error_rate"] = pm.errorRateHistory[len(pm.errorRateHistory)-1]
	}
	
	if len(pm.memoryHistory) > 0 {
		avg := uint64(0)
		for _, mem := range pm.memoryHistory {
			avg += mem
		}
		stats["avg_memory_mb"] = avg / uint64(len(pm.memoryHistory))
		stats["current_memory_mb"] = pm.memoryHistory[len(pm.memoryHistory)-1]
	}
	
	return stats
}

// Helper methods for OptimizedEngine
func (e *OptimizedEngine) getOrCreateProgress(chainType ChainType) (*ProcessProgress, error) {
	if e.progressTracker == nil {
		return &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
			ProcessingStatus:   ProcessingStatusIdle,
		}, nil
	}
	
	progress, err := e.progressTracker.GetProgress(chainType)
	if err != nil {
		// 创建新的进度记录
		progress = &ProcessProgress{
			ChainType:          chainType,
			LastProcessedBlock: big.NewInt(0),
			LastUpdateTime:     time.Now(),
			ProcessingStatus:   ProcessingStatusIdle,
		}
		
		if updateErr := e.progressTracker.UpdateProgress(chainType, progress); updateErr != nil {
			return nil, fmt.Errorf("创建进度记录失败: %w", updateErr)
		}
	}
	
	return progress, nil
}

func (e *OptimizedEngine) updateProgress(chainType ChainType, blockNumber *big.Int, txCount, eventCount int) error {
	if e.progressTracker == nil {
		return nil
	}
	
	progress := &ProcessProgress{
		ChainType:          chainType,
		LastProcessedBlock: blockNumber,
		LastUpdateTime:     time.Now(),
		TotalTransactions:  int64(txCount),
		TotalEvents:        int64(eventCount),
		ProcessingStatus:   ProcessingStatusRunning,
	}
	
	return e.progressTracker.UpdateProgress(chainType, progress)
}

func (e *OptimizedEngine) handleProcessingError(chainType ChainType, err error) {
	if e.progressTracker == nil {
		return
	}
	
	// 记录错误
	e.progressTracker.RecordError(chainType, err)
	
	// 更新处理状态为错误
	e.progressTracker.SetProcessingStatus(chainType, ProcessingStatusError)
}

func (e *OptimizedEngine) recordPerformanceMetrics(chainType ChainType, txCount int, processingTime time.Duration) {
	if e.progressTracker == nil {
		return
	}
	
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	metrics := &ProcessingMetrics{
		ChainType:        chainType,
		Timestamp:        time.Now(),
		ProcessingTime:   processingTime,
		TransactionCount: txCount,
		MemoryUsage:      int64(m.Alloc),
		CPUUsage:         0.0, // 简化实现，实际应该计算CPU使用率
	}
	
	e.progressTracker.RecordProcessingMetrics(chainType, metrics)
	
	// 记录到性能监控器
	if txCount > 0 && processingTime > 0 {
		tps := float64(txCount) / processingTime.Seconds()
		e.perfMonitor.RecordTPS(tps)
		e.perfMonitor.RecordLatency(processingTime)
	}
}
