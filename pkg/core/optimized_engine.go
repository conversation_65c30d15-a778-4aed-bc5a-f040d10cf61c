package core

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"runtime"
	"strings"
	"sync"
	"time"
)

// OptimizedEngine 优化的统一交易处理引擎
type OptimizedEngine struct {
	// 基础组件
	chainProcessors map[ChainType]ChainProcessor
	eventExtractors []BusinessEventExtractor
	storage         StorageEngine
	progressTracker ProgressTracker
	config          *EngineConfig

	// 优化组件
	batchManager  *BatchManager
	retryManager  *RetryManager
	memoryManager *MemoryManager
	perfMonitor   *PerformanceMonitor

	// 运行状态
	running bool
	mu      sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
}

// BatchManager 批处理管理器
type BatchManager struct {
	// 动态批处理大小
	baseBatchSize   int
	maxBatchSize    int
	minBatchSize    int
	adaptiveEnabled bool

	// 性能统计
	avgProcessingTime time.Duration
	successRate       float64
	lastAdjustment    time.Time

	mu sync.RWMutex
}

// RetryManager 重试管理器
type RetryManager struct {
	maxRetries      int
	baseDelay       time.Duration
	maxDelay        time.Duration
	backoffFactor   float64
	retryableErrors map[string]bool

	// 统计信息
	totalRetries      int64
	successfulRetries int64
	failedRetries     int64

	mu sync.RWMutex
}

// MemoryManager 内存管理器
type MemoryManager struct {
	maxMemoryUsage  uint64  // 最大内存使用量(MB)
	gcThreshold     float64 // GC触发阈值
	monitorInterval time.Duration

	// 统计信息
	currentUsage uint64
	peakUsage    uint64
	gcCount      uint32
	lastGCTime   time.Time

	mu sync.RWMutex
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	// 监控指标
	tpsHistory       []float64
	latencyHistory   []time.Duration
	errorRateHistory []float64
	memoryHistory    []uint64

	// 配置
	historySize     int
	monitorInterval time.Duration

	mu sync.RWMutex
}

// NewOptimizedEngine 创建优化的处理引擎
func NewOptimizedEngine(config *EngineConfig) *OptimizedEngine {
	ctx, cancel := context.WithCancel(context.Background())

	engine := &OptimizedEngine{
		chainProcessors: make(map[ChainType]ChainProcessor),
		eventExtractors: make([]BusinessEventExtractor, 0),
		config:          config,
		ctx:             ctx,
		cancel:          cancel,

		// 初始化优化组件
		batchManager:  newBatchManager(config),
		retryManager:  newRetryManager(config),
		memoryManager: newMemoryManager(config),
		perfMonitor:   newPerformanceMonitor(config),
	}

	return engine
}

// newBatchManager 创建批处理管理器
func newBatchManager(config *EngineConfig) *BatchManager {
	return &BatchManager{
		baseBatchSize:   config.BatchSize,
		maxBatchSize:    config.BatchSize * 3,
		minBatchSize:    config.BatchSize / 3,
		adaptiveEnabled: true,
		lastAdjustment:  time.Now(),
	}
}

// newRetryManager 创建重试管理器
func newRetryManager(config *EngineConfig) *RetryManager {
	retryableErrors := map[string]bool{
		"connection": true,
		"timeout":    true,
		"rate_limit": true,
		"temporary":  true,
		"network":    true,
	}

	return &RetryManager{
		maxRetries:      5,
		baseDelay:       time.Second,
		maxDelay:        time.Minute,
		backoffFactor:   2.0,
		retryableErrors: retryableErrors,
	}
}

// newMemoryManager 创建内存管理器
func newMemoryManager(config *EngineConfig) *MemoryManager {
	return &MemoryManager{
		maxMemoryUsage:  2048, // 2GB
		gcThreshold:     0.8,  // 80%
		monitorInterval: time.Second * 30,
	}
}

// newPerformanceMonitor 创建性能监控器
func newPerformanceMonitor(config *EngineConfig) *PerformanceMonitor {
	return &PerformanceMonitor{
		historySize:      100,
		monitorInterval:  time.Second * 10,
		tpsHistory:       make([]float64, 0, 100),
		latencyHistory:   make([]time.Duration, 0, 100),
		errorRateHistory: make([]float64, 0, 100),
		memoryHistory:    make([]uint64, 0, 100),
	}
}

// RegisterChainProcessor 注册链处理器
func (e *OptimizedEngine) RegisterChainProcessor(processor ChainProcessor) {
	e.mu.Lock()
	defer e.mu.Unlock()

	chainType := processor.GetChainType()
	e.chainProcessors[chainType] = processor
	log.Printf("✅ 注册链处理器: %s", chainType)
}

// RegisterEventExtractor 注册事件提取器
func (e *OptimizedEngine) RegisterEventExtractor(extractor BusinessEventExtractor) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.eventExtractors = append(e.eventExtractors, extractor)
	log.Printf("✅ 注册事件提取器: %v", extractor.GetSupportedProtocols())
}

// SetStorageEngine 设置存储引擎
func (e *OptimizedEngine) SetStorageEngine(storage StorageEngine) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.storage = storage
	log.Printf("✅ 设置存储引擎")
}

// SetProgressTracker 设置进度跟踪器
func (e *OptimizedEngine) SetProgressTracker(tracker ProgressTracker) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.progressTracker = tracker
	log.Printf("✅ 设置进度跟踪器")
}

// Start 启动优化引擎
func (e *OptimizedEngine) Start() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.running {
		return fmt.Errorf("引擎已经在运行")
	}

	if e.storage == nil {
		return fmt.Errorf("未设置存储引擎")
	}

	if len(e.chainProcessors) == 0 {
		return fmt.Errorf("未注册任何链处理器")
	}

	e.running = true

	// 启动优化组件
	e.startOptimizedComponents()

	// 启动每个链的处理协程
	for chainType, processor := range e.chainProcessors {
		if config, exists := e.config.ChainConfigs[chainType]; exists && !config.Enabled {
			log.Printf("⚠️ 链 %s 已禁用", chainType)
			continue
		}

		e.wg.Add(1)
		go e.processChainOptimized(chainType, processor)
		log.Printf("🚀 启动优化链处理器: %s", chainType)
	}

	log.Printf("🚀 优化的统一交易处理引擎已启动")
	return nil
}

// startOptimizedComponents 启动优化组件
func (e *OptimizedEngine) startOptimizedComponents() {
	// 启动内存监控
	e.wg.Add(1)
	go e.memoryMonitor()

	// 启动性能监控
	e.wg.Add(1)
	go e.performanceMonitor()

	log.Printf("✅ 优化组件已启动")
}

// Stop 停止引擎
func (e *OptimizedEngine) Stop() {
	e.mu.Lock()
	defer e.mu.Unlock()

	if !e.running {
		return
	}

	e.running = false
	e.cancel()

	// 等待所有协程完成
	e.wg.Wait()

	log.Printf("🛑 优化的统一交易处理引擎已停止")
}

// IsRunning 检查引擎是否在运行
func (e *OptimizedEngine) IsRunning() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.running
}

// processChainOptimized 优化的链处理逻辑
func (e *OptimizedEngine) processChainOptimized(chainType ChainType, processor ChainProcessor) {
	defer e.wg.Done()

	ticker := time.NewTicker(e.config.ProcessInterval)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			log.Printf("🛑 优化链 %s 处理器停止", chainType)
			return
		case <-ticker.C:
			if err := e.processChainBatchOptimized(chainType, processor); err != nil {
				log.Printf("❌ 处理链 %s 时出错: %v", chainType, err)

				// 记录错误并尝试恢复
				e.handleProcessingError(chainType, err)
			}
		}
	}
}

// processChainBatchOptimized 优化的批处理逻辑
func (e *OptimizedEngine) processChainBatchOptimized(chainType ChainType, processor ChainProcessor) error {
	startTime := time.Now()

	// 获取当前进度
	progress, err := e.getOrCreateProgress(chainType)
	if err != nil {
		return fmt.Errorf("获取进度失败: %w", err)
	}

	// 获取最新区块号
	latestBlock, err := processor.GetLatestBlockNumber(e.ctx)
	if err != nil {
		return fmt.Errorf("获取最新区块号失败: %w", err)
	}

	// 计算处理范围 - 使用动态批处理大小
	batchSize := e.batchManager.GetOptimalBatchSize(chainType)
	startBlock := new(big.Int).Add(progress.LastProcessedBlock, big.NewInt(1))
	endBlock := new(big.Int).Add(startBlock, big.NewInt(int64(batchSize-1)))

	if endBlock.Cmp(latestBlock) > 0 {
		endBlock = latestBlock
	}

	// 检查是否有新块需要处理
	if startBlock.Cmp(endBlock) > 0 {
		return nil
	}

	log.Printf("📊 [%s] 优化处理 区块 %s-%s (批量大小: %d)",
		strings.ToUpper(string(chainType)), startBlock.String(), endBlock.String(), batchSize)

	// 内存检查
	if !e.memoryManager.CheckMemoryAvailable() {
		log.Printf("⚠️ [%s] 内存不足，跳过本次处理", chainType)
		return nil
	}

	// 获取交易数据 - 使用重试机制
	transactions, err := e.getTransactionsWithRetry(processor, startBlock, endBlock)
	if err != nil {
		return fmt.Errorf("获取交易数据失败: %w", err)
	}

	processingTime := time.Since(startTime)

	// 更新批处理管理器统计
	e.batchManager.UpdateStats(chainType, len(transactions), processingTime, err == nil)

	// 记录性能指标
	e.recordPerformanceMetrics(chainType, len(transactions), processingTime)

	if len(transactions) > 0 {
		log.Printf("📥 [%s] %d笔交易 (%.1fs, 批量: %d)",
			strings.ToUpper(string(chainType)), len(transactions), processingTime.Seconds(), batchSize)
	}

	// 继续处理事件提取和存储...
	return e.processTransactionsOptimized(chainType, transactions, startBlock, endBlock)
}

// getTransactionsWithRetry 带重试机制的交易获取
func (e *OptimizedEngine) getTransactionsWithRetry(processor ChainProcessor, startBlock, endBlock *big.Int) ([]UnifiedTransaction, error) {
	var transactions []UnifiedTransaction
	var lastErr error

	for attempt := 0; attempt <= e.retryManager.maxRetries; attempt++ {
		if attempt > 0 {
			delay := e.retryManager.CalculateDelay(attempt)
			log.Printf("🔄 重试获取交易数据 (第%d次，延迟%v)", attempt, delay)
			time.Sleep(delay)
		}

		ctx, cancel := context.WithTimeout(e.ctx, time.Minute*5)
		txs, err := processor.GetTransactionsByBlockRange(ctx, startBlock, endBlock)
		cancel()

		if err == nil {
			transactions = txs
			if attempt > 0 {
				e.retryManager.RecordSuccess()
			}
			break
		}

		lastErr = err
		if !e.retryManager.IsRetryable(err) {
			break
		}

		e.retryManager.RecordRetry()
	}

	if lastErr != nil && len(transactions) == 0 {
		e.retryManager.RecordFailure()
		return nil, lastErr
	}

	return transactions, nil
}

// processTransactionsOptimized 优化的交易处理
func (e *OptimizedEngine) processTransactionsOptimized(chainType ChainType, transactions []UnifiedTransaction, startBlock, endBlock *big.Int) error {
	if len(transactions) == 0 {
		// 更新进度即使没有交易
		return e.updateProgress(chainType, endBlock, 0, 0)
	}

	// 并发提取事件
	dexTransactions, allEvents, err := e.extractEventsOptimized(transactions)
	if err != nil {
		return fmt.Errorf("事件提取失败: %w", err)
	}

	// 显示过滤统计
	if len(transactions) > 0 {
		filterRate := float64(len(dexTransactions)) / float64(len(transactions)) * 100
		log.Printf("🔍 [%s] DEX过滤: %d/%d交易 (%.1f%%)",
			strings.ToUpper(string(chainType)), len(dexTransactions), len(transactions), filterRate)
	}

	// 并发存储数据
	if err := e.storeDataOptimized(dexTransactions, allEvents); err != nil {
		return fmt.Errorf("数据存储失败: %w", err)
	}

	// 更新进度
	if err := e.updateProgress(chainType, endBlock, len(dexTransactions), len(allEvents)); err != nil {
		return fmt.Errorf("更新进度失败: %w", err)
	}

	// 优化存储日志
	if len(dexTransactions) > 0 || len(allEvents) > 0 {
		log.Printf("💾 [%s] 存储 %d/%d交易 %d事件",
			strings.ToUpper(string(chainType)), len(dexTransactions), len(transactions), len(allEvents))
	}

	return nil
}

// extractEventsOptimized 优化的事件提取
func (e *OptimizedEngine) extractEventsOptimized(transactions []UnifiedTransaction) ([]UnifiedTransaction, []BusinessEvent, error) {
	var dexTransactions []UnifiedTransaction
	var allEvents []BusinessEvent
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 使用工作池模式并发处理
	workerCount := runtime.NumCPU()
	if workerCount > len(transactions) {
		workerCount = len(transactions)
	}

	txChan := make(chan UnifiedTransaction, len(transactions))

	// 启动工作协程
	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for tx := range txChan {
				// 提取事件
				events := e.extractEventsFromTransaction(&tx)

				// 线程安全地添加结果
				mu.Lock()
				if len(events) > 0 {
					dexTransactions = append(dexTransactions, tx)
					allEvents = append(allEvents, events...)
				}
				mu.Unlock()
			}
		}()
	}

	// 发送任务
	for _, tx := range transactions {
		txChan <- tx
	}
	close(txChan)

	// 等待完成
	wg.Wait()

	return dexTransactions, allEvents, nil
}

// extractEventsFromTransaction 从单个交易提取事件
func (e *OptimizedEngine) extractEventsFromTransaction(tx *UnifiedTransaction) []BusinessEvent {
	var events []BusinessEvent

	for _, extractor := range e.eventExtractors {
		if !extractor.SupportsTransaction(tx) {
			continue
		}

		extractedEvents, err := extractor.ExtractEvents(e.ctx, tx)
		if err != nil {
			log.Printf("⚠️ %s 事件提取失败: %v", tx.ChainType, err)
			continue
		}

		events = append(events, extractedEvents...)
	}

	return events
}

// storeDataOptimized 优化的数据存储
func (e *OptimizedEngine) storeDataOptimized(transactions []UnifiedTransaction, events []BusinessEvent) error {
	var wg sync.WaitGroup
	var errors []error
	var mu sync.Mutex

	// 并发存储交易和事件
	if len(transactions) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := e.storage.StoreTransactions(e.ctx, transactions); err != nil {
				mu.Lock()
				errors = append(errors, fmt.Errorf("存储交易失败: %w", err))
				mu.Unlock()
			}
		}()
	}

	if len(events) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := e.storage.StoreBusinessEvents(e.ctx, events); err != nil {
				mu.Lock()
				errors = append(errors, fmt.Errorf("存储事件失败: %w", err))
				mu.Unlock()
			}
		}()
	}

	wg.Wait()

	if len(errors) > 0 {
		return fmt.Errorf("存储错误: %v", errors)
	}

	return nil
}

// memoryMonitor 内存监控协程
func (e *OptimizedEngine) memoryMonitor() {
	defer e.wg.Done()

	ticker := time.NewTicker(e.memoryManager.monitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.memoryManager.CheckAndManage()
		}
	}
}

// performanceMonitor 性能监控协程
func (e *OptimizedEngine) performanceMonitor() {
	defer e.wg.Done()

	ticker := time.NewTicker(e.performanceMonitor.monitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-e.ctx.Done():
			return
		case <-ticker.C:
			e.performanceMonitor.CollectMetrics()
		}
	}
}
