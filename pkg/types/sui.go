package types

type SuiBlock struct {
	Checkpoint   int64    `json:"checkPoint"`
	Digest       string   `json:"digest"`
	Transactions []string `json:"transactions"`
	TimestampMs  string   `json:"timestampMs"`
	Timestamp    uint64   `json:"timestamp"`
}

type SuiTransaction struct {
	Digest                  string                      `json:"digest"`
	Transaction             SuiTransactionBlockResponse `json:"transaction,omitempty"`
	RawTransaction          string                      `json:"rawTransaction,omitempty"`
	Effects                 interface{}                 `json:"effects,omitempty"`
	Events                  []*SuiEventLog              `json:"events,omitempty"`
	ObjectChanges           []ObjectChange              `json:"objectChanges,omitempty"`
	BalanceChanges          []BalanceChanges            `json:"balanceChanges,omitempty"`
	TimestampMs             string                      `json:"timestampMs,omitempty"`
	Checkpoint              int64                       `json:"checkPoint,omitempty"`
	ConfirmedLocalExecution bool                        `json:"confirmedLocalExecution,omitempty"`
}

type ObjectChange struct {
	Type       string      `json:"type"`
	Sender     string      `json:"sender"`
	Owner      ObjectOwner `json:"owner"`
	ObjectType string      `json:"objectType"`
	ObjectId   string      `json:"objectId"`
	PackageId  string      `json:"packageId"`
	Modules    []string    `json:"modules"`
	Version    string      `json:"version"`
	Previous   string      `json:"previousVersion,omitempty"`
	Digest     string      `json:"digest"`
}

type ObjectOwner struct {
	AddressOwner string      `json:"AddressOwner"`
	ObjectOwner  string      `json:"ObjectOwner"`
	Shared       ObjectShare `json:"Shared"`
}

type ObjectShare struct {
	InitialSharedVersion string `json:"initial_shared_version"`
}

type BalanceChanges struct {
	Owner    ObjectOwner `json:"owner"`
	CoinType string      `json:"coinType"`
	Amount   string      `json:"amount"`
}

type SuiEventLog struct {
	Id                EventId                `json:"id"`
	PackageId         string                 `json:"packageId"`
	TransactionModule string                 `json:"transactionModule"`
	Sender            string                 `json:"sender"`
	Type              string                 `json:"type"`
	ParsedJson        map[string]interface{} `json:"parsedJson"`
	Bcs               string                 `json:"bcs"`
	TimestampMs       string                 `json:"timestampMs"`
}

type EventId struct {
	TxDigest string `json:"txDigest"`
	EventSeq int64  `json:"eventSeq"`
}

type SuiTransactionBlockResponse struct {
	Data         interface{} `json:"data"`
	TxSignatures []string    `json:"txSignatures"`
}
