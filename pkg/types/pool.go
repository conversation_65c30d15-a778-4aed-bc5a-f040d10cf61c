package types

type Pool struct {
	Addr     string                 `json:"addr"`
	Factory  string                 `json:"factory"`
	Protocol string                 `json:"protocol"`
	Tokens   map[int]string         `json:"tokens"`
	Args     map[string]interface{} `json:"args,omitempty"`
	Extra    *PoolExtra             `json:"extra,omitempty"`
	Fee      int                    `json:"fee"` // fee in bps
}

type PoolExtra struct {
	Hash   string `json:"tx_hash"`
	From   string `json:"tx_from"`
	Time   uint64 `json:"tx_time,omitempty"`
	Stable bool   `json:"stable,omitempty"`
}
