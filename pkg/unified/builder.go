package unified

import (
	"fmt"
	"log"
	"math/big"
)

// EngineBuilder 引擎构建器 - 提供简洁的API来构建和配置引擎
type EngineBuilder struct {
	config           *UnifiedConfig
	factoryManager   *FactoryManager
	customScanners   map[ChainType]ChainScanner
	customExtractors map[string]EventExtractor
	customStorage    DataStorage
	customTracker    ProgressTracker
}

// NewEngineBuilder 创建引擎构建器
func NewEngineBuilder() *EngineBuilder {
	return &EngineBuilder{
		config: &UnifiedConfig{
			Chains:    make(map[ChainType]*ChainConfig),
			Protocols: make(map[string]*ProtocolConfig),
		},
		factoryManager:   NewFactoryManager(),
		customScanners:   make(map[ChainType]ChainScanner),
		customExtractors: make(map[string]EventExtractor),
	}
}

// WithConfig 设置配置
func (b *EngineBuilder) WithConfig(config *UnifiedConfig) *EngineBuilder {
	b.config = config
	return b
}

// EnableChain 启用链
func (b *EngineBuilder) EnableChain(chainType ChainType, rpcEndpoint string, options ...ChainOption) *EngineBuilder {
	config := &ChainConfig{
		Enabled:     true,
		RPCEndpoint: rpcEndpoint,
		ChainID:     string(chainType),
		BatchSize:   10,
		Extra:       make(map[string]interface{}),
	}

	// 应用选项
	for _, option := range options {
		option(config)
	}

	b.config.Chains[chainType] = config
	return b
}

// EnableProtocol 启用协议
func (b *EngineBuilder) EnableProtocol(protocol string, options ...ProtocolOption) *EngineBuilder {
	config := &ProtocolConfig{
		Enabled:   true,
		Contracts: make(map[string]string),
		Events:    []string{},
		Extra:     make(map[string]interface{}),
	}

	// 应用选项
	for _, option := range options {
		option(config)
	}

	b.config.Protocols[protocol] = config
	return b
}

// WithStorage 设置存储引擎
func (b *EngineBuilder) WithStorage(storageType, url, database string, options ...StorageOption) *EngineBuilder {
	config := &StorageConfig{
		Type:     storageType,
		URL:      url,
		Database: database,
		Extra:    make(map[string]interface{}),
	}

	// 应用选项
	for _, option := range options {
		option(config)
	}

	b.config.Storage = config
	return b
}

// WithProgressTracker 设置进度跟踪器
func (b *EngineBuilder) WithProgressTracker(trackerType, url string, options ...StorageOption) *EngineBuilder {
	config := &StorageConfig{
		Type:  trackerType,
		URL:   url,
		Extra: make(map[string]interface{}),
	}

	// 应用选项
	for _, option := range options {
		option(config)
	}

	b.config.Progress = config
	return b
}

// WithCustomScanner 使用自定义链扫描器
func (b *EngineBuilder) WithCustomScanner(scanner ChainScanner) *EngineBuilder {
	b.customScanners[scanner.GetChainType()] = scanner
	return b
}

// WithCustomExtractor 使用自定义事件提取器
func (b *EngineBuilder) WithCustomExtractor(extractor EventExtractor) *EngineBuilder {
	b.customExtractors[extractor.GetProtocolName()] = extractor
	return b
}

// WithCustomStorage 使用自定义存储引擎
func (b *EngineBuilder) WithCustomStorage(storage DataStorage) *EngineBuilder {
	b.customStorage = storage
	return b
}

// WithCustomProgressTracker 使用自定义进度跟踪器
func (b *EngineBuilder) WithCustomProgressTracker(tracker ProgressTracker) *EngineBuilder {
	b.customTracker = tracker
	return b
}

// Build 构建引擎
func (b *EngineBuilder) Build() (*UnifiedEngine, error) {
	// 验证配置
	if err := b.validateConfig(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	engine := NewUnifiedEngine(b.config)

	// 注册链扫描器
	if err := b.registerScanners(engine); err != nil {
		return nil, fmt.Errorf("注册链扫描器失败: %w", err)
	}

	// 注册事件提取器
	if err := b.registerExtractors(engine); err != nil {
		return nil, fmt.Errorf("注册事件提取器失败: %w", err)
	}

	// 设置存储引擎
	if err := b.setupStorage(engine); err != nil {
		return nil, fmt.Errorf("设置存储引擎失败: %w", err)
	}

	// 设置进度跟踪器
	if err := b.setupProgressTracker(engine); err != nil {
		return nil, fmt.Errorf("设置进度跟踪器失败: %w", err)
	}

	log.Printf("✅ 统一引擎构建完成")
	return engine, nil
}

// validateConfig 验证配置
func (b *EngineBuilder) validateConfig() error {
	if len(b.config.Chains) == 0 && len(b.customScanners) == 0 {
		return fmt.Errorf("至少需要配置一个链")
	}

	if len(b.config.Protocols) == 0 && len(b.customExtractors) == 0 {
		return fmt.Errorf("至少需要配置一个协议")
	}

	if b.config.Storage == nil && b.customStorage == nil {
		return fmt.Errorf("必须配置存储引擎")
	}

	if b.config.Progress == nil && b.customTracker == nil {
		return fmt.Errorf("必须配置进度跟踪器")
	}

	return nil
}

// registerScanners 注册链扫描器
func (b *EngineBuilder) registerScanners(engine *UnifiedEngine) error {
	// 注册自定义扫描器
	for chainType, scanner := range b.customScanners {
		if err := engine.RegisterScanner(scanner); err != nil {
			return fmt.Errorf("注册自定义扫描器失败 %s: %w", chainType, err)
		}
	}

	// 注册配置的扫描器
	for chainType, chainConfig := range b.config.Chains {
		if !chainConfig.Enabled {
			continue
		}

		// 跳过已有自定义扫描器的链
		if _, exists := b.customScanners[chainType]; exists {
			continue
		}

		scanner, err := b.factoryManager.ChainScannerFactory.CreateScanner(chainType, chainConfig)
		if err != nil {
			return fmt.Errorf("创建链扫描器失败 %s: %w", chainType, err)
		}

		if err := engine.RegisterScanner(scanner); err != nil {
			return fmt.Errorf("注册链扫描器失败 %s: %w", chainType, err)
		}
	}

	return nil
}

// registerExtractors 注册事件提取器
func (b *EngineBuilder) registerExtractors(engine *UnifiedEngine) error {
	// 注册自定义提取器
	for protocol, extractor := range b.customExtractors {
		if err := engine.RegisterExtractor(extractor); err != nil {
			return fmt.Errorf("注册自定义提取器失败 %s: %w", protocol, err)
		}
	}

	// 注册配置的提取器
	for protocol, protocolConfig := range b.config.Protocols {
		if !protocolConfig.Enabled {
			continue
		}

		// 跳过已有自定义提取器的协议
		if _, exists := b.customExtractors[protocol]; exists {
			continue
		}

		extractor, err := b.factoryManager.EventExtractorFactory.CreateExtractor(protocol, protocolConfig)
		if err != nil {
			return fmt.Errorf("创建事件提取器失败 %s: %w", protocol, err)
		}

		if err := engine.RegisterExtractor(extractor); err != nil {
			return fmt.Errorf("注册事件提取器失败 %s: %w", protocol, err)
		}
	}

	return nil
}

// setupStorage 设置存储引擎
func (b *EngineBuilder) setupStorage(engine *UnifiedEngine) error {
	if b.customStorage != nil {
		engine.SetStorage(b.customStorage)
		return nil
	}

	if b.config.Storage != nil {
		storage, err := b.factoryManager.DataStorageFactory.CreateStorage(b.config.Storage)
		if err != nil {
			return fmt.Errorf("创建存储引擎失败: %w", err)
		}

		engine.SetStorage(storage)
	}

	return nil
}

// setupProgressTracker 设置进度跟踪器
func (b *EngineBuilder) setupProgressTracker(engine *UnifiedEngine) error {
	if b.customTracker != nil {
		engine.SetProgressTracker(b.customTracker)
		return nil
	}

	if b.config.Progress != nil {
		tracker, err := b.factoryManager.ProgressTrackerFactory.CreateTracker(b.config.Progress)
		if err != nil {
			return fmt.Errorf("创建进度跟踪器失败: %w", err)
		}

		engine.SetProgressTracker(tracker)
	}

	return nil
}

// ===== 配置选项 =====

// ChainOption 链配置选项
type ChainOption func(*ChainConfig)

// WithChainID 设置链ID
func WithChainID(chainID string) ChainOption {
	return func(config *ChainConfig) {
		config.ChainID = chainID
	}
}

// WithBatchSize 设置批次大小
func WithBatchSize(batchSize int) ChainOption {
	return func(config *ChainConfig) {
		config.BatchSize = batchSize
	}
}

// WithStartBlock 设置起始区块
func WithStartBlock(startBlock int64) ChainOption {
	return func(config *ChainConfig) {
		config.StartBlock = big.NewInt(startBlock)
	}
}

// WithChainExtra 设置链扩展配置
func WithChainExtra(key string, value interface{}) ChainOption {
	return func(config *ChainConfig) {
		config.Extra[key] = value
	}
}

// ProtocolOption 协议配置选项
type ProtocolOption func(*ProtocolConfig)

// WithContracts 设置合约地址
func WithContracts(contracts map[string]string) ProtocolOption {
	return func(config *ProtocolConfig) {
		for k, v := range contracts {
			config.Contracts[k] = v
		}
	}
}

// WithEvents 设置事件列表
func WithEvents(events []string) ProtocolOption {
	return func(config *ProtocolConfig) {
		config.Events = events
	}
}

// WithProtocolExtra 设置协议扩展配置
func WithProtocolExtra(key string, value interface{}) ProtocolOption {
	return func(config *ProtocolConfig) {
		config.Extra[key] = value
	}
}

// StorageOption 存储配置选项
type StorageOption func(*StorageConfig)

// WithStorageExtra 设置存储扩展配置
func WithStorageExtra(key string, value interface{}) StorageOption {
	return func(config *StorageConfig) {
		config.Extra[key] = value
	}
}
