# InfluxDB多链Bucket配置指南

## 概述

为了实现不同区块链数据的完全隔离，本项目已配置为每个链使用独立的InfluxDB bucket进行数据存储。这样可以：

- 避免不同链的数据混合
- 便于独立管理和查询各链的交易和事件数据
- 提高数据查询性能
- 简化数据备份和恢复

## Bucket命名规范

每个链都有自己专用的bucket：

| 区块链 | Bucket名称 | 配置文件 |
|--------|------------|----------|
| Sui | `sui` | `configs/sui.yaml` |
| Ethereum | `ethereum` | `configs/ethereum.yaml` |
| BSC | `bsc` | `configs/bsc.yaml` |
| Solana | `solana` | `configs/solana.yaml` |
| 传统模式 | `blockchain-data` | `configs/config.yaml` |

## 配置文件更新

### 链特定配置文件

每个链的配置文件中的InfluxDB配置已更新：

```yaml
# 示例：configs/sui.yaml
storage:
  type: "influxdb"
  influxdb:
    url: "http://localhost:8086"
    token: "unified-tx-parser-token-2024"
    org: "unified-tx-parser"
    bucket: "sui"  # Sui链专用bucket
    batch_size: 1000
    flush_time: 10
    precision: "ms"
```

### 传统配置文件

主配置文件 `configs/config.yaml` 保持使用通用bucket `blockchain-data`，确保向后兼容性。

## Docker配置更新

### InfluxDB初始化

添加了自动bucket创建脚本 `docker/influxdb/init-buckets.sh`，在InfluxDB容器启动时自动创建所有需要的bucket：

- `sui`
- `ethereum`
- `bsc`
- `solana`
- `blockchain-data`（默认bucket）

### Grafana数据源

更新了Grafana数据源配置，为每个链创建了专用的数据源：

- `InfluxDB` - 通用数据源（默认）
- `InfluxDB-Sui` - Sui链专用
- `InfluxDB-Ethereum` - Ethereum链专用
- `InfluxDB-BSC` - BSC链专用
- `InfluxDB-Solana` - Solana链专用

## 使用方法

### 启动链特定服务

使用链特定配置启动服务：

```bash
# 启动Sui链解析器
./unified-tx-parser -chain sui

# 或使用环境变量
CHAIN_TYPE=sui ./unified-tx-parser

# 或使用Docker
docker run -e CHAIN_TYPE=sui unified-tx-parser
```

### 启动多链服务

使用Docker Compose启动所有链服务：

```bash
# 启动所有链服务
docker-compose -f docker/docker-compose-chains.yml up -d

# 启动简化版本（单服务多链）
docker-compose -f docker/docker-compose-simple.yml up -d
```

### 数据查询

在Grafana中，可以选择对应的数据源来查询特定链的数据：

1. 选择对应链的数据源（如 `InfluxDB-Sui`）
2. 查询该链的交易和事件数据
3. 数据完全隔离，不会混合其他链的数据

### 手动创建Bucket

如果需要手动创建bucket，可以使用InfluxDB CLI：

```bash
# 进入InfluxDB容器
docker exec -it unified-tx-parser-influxdb bash

# 创建新的bucket
influx bucket create \
  --name new-chain \
  --org unified-tx-parser \
  --retention 90d \
  --token unified-tx-parser-token-2024
```

## 数据迁移

如果之前使用的是单一bucket，需要迁移数据：

1. 备份现有数据
2. 根据chain_type标签将数据分离到对应的bucket
3. 验证数据完整性

## 监控和维护

### 检查Bucket状态

```bash
# 列出所有bucket
influx bucket list --org unified-tx-parser --token unified-tx-parser-token-2024

# 检查特定bucket的数据量
influx query 'from(bucket:"sui") |> range(start:-1h) |> count()' \
  --org unified-tx-parser --token unified-tx-parser-token-2024
```

### 清理数据

```bash
# 删除特定时间范围的数据
influx delete --bucket sui \
  --start 2024-01-01T00:00:00Z \
  --stop 2024-01-02T00:00:00Z \
  --org unified-tx-parser \
  --token unified-tx-parser-token-2024
```

## 故障排除

### Bucket不存在错误

如果遇到bucket不存在的错误：

1. 检查配置文件中的bucket名称是否正确
2. 确认InfluxDB中已创建对应的bucket
3. 检查初始化脚本是否正确执行

### 权限错误

确保使用的token有足够的权限访问对应的bucket和组织。

### 连接错误

检查InfluxDB服务是否正常运行，网络连接是否正常。
