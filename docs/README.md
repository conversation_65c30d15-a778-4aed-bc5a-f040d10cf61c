# 统一交易解析器 (Unified Transaction Parser)

## 📋 项目概述

统一交易解析器是一个高性能、多链支持的区块链交易数据处理系统，专门用于从多个区块链网络中提取、解析和存储DeFi协议的交易数据和业务事件。

### 🎯 核心特性

- **多链支持**: 支持Ethereum、BSC、Solana、Sui等主流区块链
- **协议解析**: 内置Uniswap、PancakeSwap、Jupiter、Bluefin等主流DEX协议解析器
- **智能过滤**: 只存储包含DEX事件的交易，节省98%存储空间
- **实时处理**: 支持实时和批量处理模式
- **高可用性**: 支持故障恢复、进度跟踪、健康检查
- **多存储引擎**: 支持MySQL、InfluxDB、Redis等多种存储方案
- **容器化部署**: 完整的Docker和Docker Compose支持
- **监控告警**: 内置指标收集、健康检查和告警系统

### 🏗️ 系统架构

系统采用分层架构设计，包含以下核心层次：

1. **用户接口层**: REST API、命令行工具、Web Dashboard
2. **核心引擎层**: 统一处理引擎、配置管理、进度跟踪
3. **区块链处理层**: 各链的专用处理器
4. **协议解析层**: DeFi协议的事件提取器
5. **存储层**: 多种存储引擎支持
6. **监控层**: 指标收集、健康检查、日志系统

## 🚀 快速开始

### 环境要求

- Go 1.21+
- Docker & Docker Compose
- MySQL 8.0+ (可选)
- InfluxDB 2.0+ (可选)
- Redis 6.0+

### 安装部署

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/unified-tx-parser.git
cd unified-tx-parser
```

#### 2. 配置文件
```bash
# 复制配置文件
cp configs/config.yaml.example configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

#### 3. Docker部署（推荐）
```bash
# 启动完整服务栈
docker-compose -f docker/docker-compose-simple.yml up -d

# 或使用启动脚本
./scripts/start-with-influxdb.sh
```

#### 4. 本地开发
```bash
# 安装依赖
go mod tidy

# 验证配置
go run cmd/validate-config/main.go

# 启动服务
go run cmd/parser/main.go
```

### 配置说明

#### 基础配置
```yaml
app:
  name: "unified-tx-parser"
  version: "2.0.0"
  port: 8081

# 启用的区块链
chains:
  bsc:
    enabled: true
    rpc_endpoint: "https://bsc.publicnode.com"
    batch_size: 10

# 存储配置
storage:
  type: "influxdb"  # mysql, influxdb, memory
  influxdb:
    url: "http://localhost:8086"
    token: "your-token"
    org: "unified-tx-parser"
    bucket: "blockchain-data"
```

## 📊 数据处理流程

### 处理周期

1. **获取进度**: 从Redis获取上次处理的区块位置
2. **计算范围**: 确定本次处理的区块范围
3. **获取交易**: 批量从区块链网络获取交易数据
4. **事件提取**: 通过协议处理器提取DEX事件
5. **智能过滤**: 只保留包含DEX事件的交易
6. **数据存储**: 存储过滤后的交易和事件数据
7. **进度更新**: 更新处理进度到Redis

### 日志格式

系统使用统一的日志格式，便于监控和调试：

```
📊 [CHAIN] 区块 start-end
📥 [CHAIN] X笔交易 (X.Xs)
🔍 [CHAIN] DEX过滤: Y/X交易 (Z.Z%)
💾 [CHAIN] 存储 Y/X交易 Z事件
✅ [CHAIN] Y/X交易 Z事件 | DEX:count (区块:block)
```

## 🔧 核心组件

### 1. 统一处理引擎 (Engine)

核心处理引擎负责协调各个组件，管理处理流程：

- **多链并发处理**: 支持多个区块链同时处理
- **故障恢复**: 自动重试和错误处理
- **进度管理**: 精确的进度跟踪和恢复
- **性能监控**: 实时性能指标收集

### 2. 链处理器 (ChainProcessor)

每个区块链都有专用的处理器：

- **Sui处理器**: 处理Sui网络交易
- **以太坊处理器**: 处理Ethereum网络交易
- **BSC处理器**: 处理Binance Smart Chain交易
- **Solana处理器**: 处理Solana网络交易

### 3. 协议解析器 (ProtocolHandler)

支持主流DeFi协议的事件解析：

- **Uniswap**: V2/V3协议支持
- **PancakeSwap**: BSC上的主流DEX
- **Jupiter**: Solana生态的聚合器
- **Bluefin**: Sui生态的DEX协议

### 4. 存储引擎 (StorageEngine)

支持多种存储方案：

- **MySQL**: 关系型数据库，适合复杂查询
- **InfluxDB**: 时序数据库，适合大规模数据和分析
- **内存存储**: 开发测试用，数据不持久化

### 5. 进度跟踪器 (ProgressTracker)

基于Redis的进度管理：

- **断点续传**: 支持服务重启后从断点继续
- **多链进度**: 独立跟踪每个链的处理进度
- **统计信息**: 处理速度、成功率等统计
- **错误记录**: 详细的错误历史和恢复状态

## 📈 性能特性

### 存储优化

- **智能过滤**: 只存储包含DEX事件的交易，节省98%存储空间
- **批量写入**: 支持批量写入，提高写入性能
- **压缩存储**: InfluxDB支持数据压缩

### 处理性能

- **并发处理**: 支持多链并发处理
- **批量获取**: 批量获取区块数据，减少网络请求
- **缓存机制**: Redis缓存热点数据

### 监控指标

- **处理速度**: TPS (Transactions Per Second)
- **过滤效率**: DEX交易过滤比例
- **错误率**: 处理错误统计
- **资源使用**: CPU、内存、网络使用情况

## 🔍 API接口

### 健康检查
```bash
GET /health
```

### 获取处理进度
```bash
GET /api/v1/progress
GET /api/v1/progress/{chain}
```

### 查询交易数据
```bash
GET /api/v1/transactions?chain=bsc&limit=100
GET /api/v1/transactions/{hash}
```

### 查询事件数据
```bash
GET /api/v1/events?protocol=PancakeSwap&limit=100
GET /api/v1/events/types/{type}
```

### 系统统计
```bash
GET /api/v1/stats
GET /api/v1/stats/{chain}
```

## 🐳 Docker部署

### 完整服务栈

使用Docker Compose部署完整的服务栈：

```bash
# 启动所有服务
docker-compose -f docker/docker-compose-simple.yml up -d

# 查看服务状态
docker-compose -f docker/docker-compose-simple.yml ps

# 查看日志
docker-compose -f docker/docker-compose-simple.yml logs -f unified-tx-parser
```

### 服务访问

- **API服务**: http://localhost:8081
- **InfluxDB**: http://localhost:8086
- **Grafana**: http://localhost:3000
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## 📝 配置管理

### 环境特定配置

- `configs/development.yaml`: 开发环境配置
- `configs/production.yaml`: 生产环境配置
- `configs/docker-influxdb-config.yaml`: Docker环境配置

### 配置验证

```bash
# 验证配置文件
go run cmd/validate-config/main.go configs/config.yaml

# 输出示例
🔍 配置文件验证工具
==================
✅ 配置验证通过，无错误
📋 配置摘要显示
🌍 环境变量检查
```

### 环境变量支持

生产环境支持环境变量替换：

```yaml
mysql:
  host: "${MYSQL_HOST}"
  password: "${MYSQL_PASSWORD}"
```

## 🔧 开发指南

### 添加新的区块链支持

1. 实现 `ChainProcessor` 接口
2. 在 `pkg/chains/` 下创建新的处理器
3. 注册到引擎中
4. 更新配置文件

### 添加新的协议支持

1. 实现 `ProtocolHandler` 接口
2. 在 `pkg/dexs/` 下创建新的处理器
3. 注册到DEX提取器中
4. 定义事件结构体

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包测试
go test ./pkg/core/...

# 运行基准测试
go test -bench=. ./pkg/core/...
```

## 📚 相关文档

- [配置文件完整指南](configuration-guide.md)
- [API接口文档](api-reference.md)
- [部署运维指南](deployment-guide.md)
- [开发者指南](developer-guide.md)
- [故障排除指南](troubleshooting.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- 问题反馈: [GitHub Issues](https://github.com/your-org/unified-tx-parser/issues)
- 文档: [项目Wiki](https://github.com/your-org/unified-tx-parser/wiki)
- 邮件: <EMAIL>
